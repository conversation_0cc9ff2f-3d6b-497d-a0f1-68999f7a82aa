import 'package:equatable/equatable.dart';

/// Base class for all failures in the application
abstract class Failure extends Equatable {
  final String message;
  final int? code;

  const Failure(this.message, {this.code});

  @override
  List<Object?> get props => [message, code];

  @override
  String toString() => 'Failure(message: $message, code: $code)';
}

/// Failure related to server/network issues
class ServerFailure extends Failure {
  const ServerFailure(String message, {int? code}) : super(message, code: code);

  @override
  String toString() => 'ServerFailure(message: $message, code: $code)';
}

/// Failure related to caching
class CacheFailure extends Failure {
  const CacheFailure(String message, {int? code}) : super(message, code: code);

  @override
  String toString() => 'CacheFailure(message: $message, code: $code)';
}

/// Failure related to network connectivity
class NetworkFailure extends Failure {
  const NetworkFailure(String message, {int? code}) : super(message, code: code);

  @override
  String toString() => 'NetworkFailure(message: $message, code: $code)';
}

/// Failure related to authentication
class AuthFailure extends Failure {
  const AuthFailure(String message, {int? code}) : super(message, code: code);

  @override
  String toString() => 'AuthFailure(message: $message, code: $code)';
}

/// Failure related to validation
class ValidationFailure extends Failure {
  const ValidationFailure(String message, {int? code}) : super(message, code: code);

  @override
  String toString() => 'ValidationFailure(message: $message, code: $code)';
}

/// Failure related to permissions
class PermissionFailure extends Failure {
  const PermissionFailure(String message, {int? code}) : super(message, code: code);

  @override
  String toString() => 'PermissionFailure(message: $message, code: $code)';
}

/// Failure related to AI/ML operations
class AIFailure extends Failure {
  const AIFailure(String message, {int? code}) : super(message, code: code);

  @override
  String toString() => 'AIFailure(message: $message, code: $code)';
}

/// Failure related to data parsing/formatting
class DataFailure extends Failure {
  const DataFailure(String message, {int? code}) : super(message, code: code);

  @override
  String toString() => 'DataFailure(message: $message, code: $code)';
}

/// Generic failure for unexpected errors
class UnknownFailure extends Failure {
  const UnknownFailure(String message, {int? code}) : super(message, code: code);

  @override
  String toString() => 'UnknownFailure(message: $message, code: $code)';
}
