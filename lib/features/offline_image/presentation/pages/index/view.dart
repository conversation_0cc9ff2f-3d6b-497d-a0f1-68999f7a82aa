import 'dart:convert';
import 'dart:io';

import 'package:diogeneschatbot/features/image_generation/models/image_generation_settings.dart';
import 'package:diogeneschatbot/features/image_generation/models/image_style_presets.dart';
import 'package:diogeneschatbot/features/image_generation/widgets/style_settings_widget.dart';
import 'package:diogeneschatbot/features/offline_image/data/local/data_sources/prompt.dart';
import 'package:diogeneschatbot/features/offline_image/domain/controller/model_service.dart';
import 'package:diogeneschatbot/features/offline_image/presentation/pages/index/controller.dart';
import 'package:diogeneschatbot/features/offline_image/presentation/pages/index/downloader.dart';
import 'package:diogeneschatbot/features/offline_image/presentation/pages/index/generator.dart';
import 'package:diogeneschatbot/features/offline_image/presentation/pages/index/loader.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/widgets/enhanced_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get_it/get_it.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

// TODO: Add image gallery for viewing generated images history
// TODO: Implement image favorites and rating system
// TODO: Add batch generation capabilities
// TODO: Implement advanced model settings and configurations
// TODO: Add image editing tools (crop, resize, filters)
// TODO: Implement model performance monitoring and statistics

final GetIt getIt = GetIt.instance;

/// Enhanced offline image generation page with modern UI and improved user experience
class OfflineImageGenerationPage extends StatefulWidget {
  const OfflineImageGenerationPage({super.key});

  @override
  State<OfflineImageGenerationPage> createState() =>
      _OfflineImageGenerationPageState();
}

class _OfflineImageGenerationPageState
    extends State<OfflineImageGenerationPage> {
  IndexPageController controller = getIt<IndexPageController>();
  final ModelService modelService = GetIt.instance<ModelService>();
  final IndexPageModelDownloader downloader =
      GetIt.instance<IndexPageModelDownloader>();
  final IndexPageModelLoader loader = GetIt.instance<IndexPageModelLoader>();
  final IndexPageGenerator generator = GetIt.instance<IndexPageGenerator>();

  // Enhanced style settings using the same system as OpenAI generation
  ImageGenerationSettings _generationSettings = const ImageGenerationSettings(
    styleCategory: StyleCategory.photorealistic,
    selectedStyle: 'Photography',
    useAdvancedPrompting: true,
  );

  // Enhanced prompt preview
  String _enhancedPromptPreview = '';
  bool _showEnhancedPrompt = false;

  // Default selection
  @override
  void initState() {
    super.initState();
    controller.addListener(update); // Listen for changes in the controller
    downloader.addListener(update);
    loader.addListener(update);
    generator.addListener(update);

    // Initialize with photography style
    controller.negativePromptTextController.text =
        Prompt.photography_negative_prompt;
    controller.promptTextController.text = "A beautiful photograph";

    // Update enhanced prompt preview
    _updateEnhancedPromptPreview();

    _initializeController(); // Initialize controller asynchronously
  }

  Future<void> _initializeController() async {
    await controller.init();

    setState(() {}); // Trigger rebuild after controller is ready
  }

  @override
  void dispose() {
    controller.removeListener(update); // Clean up the listener
    super.dispose();
  }

  void update() {
    setState(() {
      // This will trigger a rebuild of the UI
    });
  }

  /// Updates the enhanced prompt preview using the style settings
  void _updateEnhancedPromptPreview() {
    if (controller.promptTextController.text.isNotEmpty) {
      _enhancedPromptPreview = ImageStylePresets.buildEnhancedPrompt(
        controller.promptTextController.text,
        _generationSettings,
      );
    } else {
      _enhancedPromptPreview = '';
    }
  }

  Widget buildInputWidgets(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Enhanced Prompt Input
        const Text(
          "Prompt",
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
        ),
        const SizedBox(height: 8),
        _buildExpandableTextField(
          controller.promptTextController,
          'Describe what you want to generate...',
          onChanged: (value) {
            _updateEnhancedPromptPreview();
          },
        ),

        const SizedBox(height: 16),

        // Enhanced Prompt Preview
        if (_showEnhancedPrompt && _enhancedPromptPreview.isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.primaryGreen.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppTheme.primaryGreen.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.auto_awesome,
                      color: AppTheme.primaryGreen,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Enhanced Prompt Preview',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryGreen,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  _enhancedPromptPreview,
                  style: const TextStyle(fontSize: 13),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
        ],

        // Toggle Enhanced Prompt Preview
        Row(
          children: [
            Checkbox(
              value: _showEnhancedPrompt,
              onChanged: (value) {
                setState(() {
                  _showEnhancedPrompt = value ?? false;
                  if (_showEnhancedPrompt) {
                    _updateEnhancedPromptPreview();
                  }
                });
              },
              activeColor: AppTheme.primaryGreen,
            ),
            const Text('Show Enhanced Prompt Preview'),
          ],
        ),

        // Enhanced Style Settings using shared widget
        StyleSettingsWidget(
          settings: _generationSettings,
          onSettingsChanged: (newSettings) {
            setState(() {
              _generationSettings = newSettings;
              _updateEnhancedPromptPreview();
            });
          },
          showAdvancedOptions: true,
          compact: false,
        ),

        const SizedBox(height: 16),

        // Negative Prompt
        const Text(
          "Negative Prompt",
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
        ),
        const SizedBox(height: 8),
        _buildExpandableTextField(
          controller.negativePromptTextController,
          'What to avoid in the image...',
        ),

        const SizedBox(height: 16),
        const Divider(),
        const SizedBox(height: 16),

        // Generation Parameters
        Row(
          children: [
            Expanded(
              child: _buildTextField(
                controller.stepCountTextController,
                'Step count:',
                keyboardType: TextInputType.number,
                onChanged: (text) {
                  if (text.isEmpty) {
                    controller
                        .stepCountTextController
                        .value = const TextEditingValue(
                      text: "1",
                      selection: TextSelection(baseOffset: 0, extentOffset: 1),
                    );
                  }
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        const Text("Adjust Step Count", style: TextStyle(fontSize: 16)),
        Slider(
          value:
              double.tryParse(controller.stepCountTextController.text) ?? 1.0,
          min: 1.0,
          max: 100.0,
          divisions: 99,
          label: controller.stepCountTextController.text,
          onChanged: (value) {
            setState(() {
              controller.stepCountTextController.text = value
                  .toInt()
                  .toString();
            });
          },
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            const SizedBox(width: 8),
            Expanded(
              child: _buildTextField(
                controller.scaleTextController,
                'Guidance scale:',
                keyboardType: TextInputType.number,
                onChanged: (text) {
                  if (text.isEmpty) {
                    controller
                        .scaleTextController
                        .value = const TextEditingValue(
                      text: "1",
                      selection: TextSelection(baseOffset: 0, extentOffset: 1),
                    );
                  }
                },
              ),
            ),
          ],
        ),
        const Text("Adjust Guidance Scale", style: TextStyle(fontSize: 16)),
        Slider(
          value: double.tryParse(controller.scaleTextController.text) ?? 1.0,
          min: 0.0,
          max: 10.0,
          divisions: 20,
          label: controller.scaleTextController.text,
          onChanged: (value) {
            setState(() {
              controller.scaleTextController.text = value.toStringAsFixed(1);
            });
          },
        ),
      ],
    );
  }

  Widget _buildTextField(
    TextEditingController controller,
    String hint, {
    TextInputType keyboardType = TextInputType.text,
    ValueChanged<String>? onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 4,
            spreadRadius: 1,
            offset: const Offset(2, 2),
          ),
        ],
      ),
      height: 50,
      alignment: Alignment.centerLeft,
      child: TextField(
        controller: controller,
        keyboardType: keyboardType,
        onChanged: onChanged,
        decoration: InputDecoration(
          isDense: true,
          border: InputBorder.none,
          hintText: hint,
          contentPadding: const EdgeInsets.symmetric(horizontal: 12),
        ),
      ),
    );
  }

  /// Builds an expandable text field that can grow with content
  Widget _buildExpandableTextField(
    TextEditingController controller,
    String hint, {
    ValueChanged<String>? onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 4,
            spreadRadius: 1,
            offset: const Offset(2, 2),
          ),
        ],
      ),
      child: TextField(
        controller: controller,
        maxLines: null, // Allow unlimited lines
        minLines: 3, // Start with 3 lines
        keyboardType: TextInputType.multiline,
        onChanged: onChanged,
        decoration: InputDecoration(
          border: InputBorder.none,
          hintText: hint,
          contentPadding: const EdgeInsets.all(12),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? AppTheme.darkSurface : AppTheme.lightSurface,
      appBar: AppBarStyles.primary(
        title: 'Offline Image Generation',
        actions: [
          IconButton(
            icon: Icon(Icons.info_outline, color: Colors.white),
            tooltip: 'Help & Tips',
            onPressed: () => _showHelpDialog(context),
          ),
          IconButton(
            icon: Icon(Icons.history, color: Colors.white),
            tooltip: 'Generation History',
            onPressed: () => _showHistoryDialog(context),
          ),
        ],
      ),
      body: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) currentFocus.unfocus();
        },
        child: LayoutBuilder(
          builder: (context, view) {
            if (Platform.isIOS || Platform.isMacOS) {
              return SingleChildScrollView(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  constraints: BoxConstraints(minHeight: view.maxHeight),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Enhanced Image Display Area
                      _buildImageDisplayArea(context, theme),

                      const SizedBox(height: 20),
                      // Error Message Display
                      if (controller.errorMessage != null)
                        Container(
                          margin: const EdgeInsets.only(bottom: 16),
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.red.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Colors.red.withValues(alpha: 0.3),
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.error_outline, color: Colors.red),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  controller.errorMessage!,
                                  style: TextStyle(
                                    color: Colors.red,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                      // Enhanced Controls Section
                      _buildControlsSection(context, theme),
                    ],
                  ),
                ),
              );
            }
            return const Center(child: Text("Unsupported platform."));
          },
        ),
      ),
    );
  }

  /// Shows help dialog with tips and instructions
  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.help_outline, color: AppTheme.primaryGreen),
            SizedBox(width: 8),
            Text('Help & Tips'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHelpSection('Getting Started', [
                'Select or download a model first',
                'Enter a descriptive prompt',
                'Choose an image style',
                'Adjust generation parameters',
                'Click Generate to create your image',
              ]),
              SizedBox(height: 16),
              _buildHelpSection('Tips for Better Results', [
                'Be specific in your descriptions',
                'Include style keywords (realistic, cartoon, etc.)',
                'Mention lighting and mood',
                'Specify colors and composition',
                'Use negative prompts to exclude unwanted elements',
              ]),
              SizedBox(height: 16),
              _buildHelpSection('Model Management', [
                'Download models for offline generation',
                'Delete unused models to save space',
                'Check model status (downloaded/not downloaded)',
                'Larger models generally produce better quality',
              ]),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Got it!'),
          ),
        ],
      ),
    );
  }

  /// Builds help section with title and tips
  Widget _buildHelpSection(String title, List<String> tips) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
        SizedBox(height: 8),
        ...tips.map(
          (tip) => Padding(
            padding: EdgeInsets.symmetric(vertical: 2),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.check_circle_outline,
                  size: 16,
                  color: AppTheme.secondaryGreen,
                ),
                SizedBox(width: 8),
                Expanded(child: Text(tip)),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Shows generation history dialog (placeholder for future implementation)
  void _showHistoryDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.history, color: AppTheme.primaryGreen),
            SizedBox(width: 8),
            Text('Generation History'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.construction,
              size: 48,
              color: AppTheme.primaryGreen.withValues(alpha: 0.6),
            ),
            SizedBox(height: 16),
            Text(
              'Coming Soon!',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryGreen,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Image generation history will be available in a future update.',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600]),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Builds enhanced image display area with modern styling
  Widget _buildImageDisplayArea(BuildContext context, ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Container(
          constraints: const BoxConstraints(minHeight: 300),
          child: generator.generateImage != null
              ? Column(
                  children: [
                    // Generated Image
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: theme.scaffoldBackgroundColor,
                      ),
                      child: Image.memory(
                        generator.generateImage!,
                        fit: BoxFit.contain,
                      ),
                    ),

                    // Action Buttons
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: theme.cardColor,
                        border: Border(
                          top: BorderSide(color: theme.dividerColor, width: 1),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildActionButton(
                            icon: Icons.copy,
                            label: 'Copy',
                            onPressed: () => _copyImageToClipboard(context),
                            color: AppTheme.primaryGreen,
                          ),
                          _buildActionButton(
                            icon: Icons.share,
                            label: 'Share',
                            onPressed: () => _shareImage(context),
                            color: AppTheme.secondaryGreen,
                          ),
                          _buildActionButton(
                            icon: Icons.download,
                            label: 'Save',
                            onPressed: () => _saveImage(context),
                            color: Colors.blue,
                          ),
                        ],
                      ),
                    ),
                  ],
                )
              : _buildEmptyImageArea(theme),
        ),
      ),
    );
  }

  /// Builds action button for image operations
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            icon: Icon(icon, color: color),
            onPressed: onPressed,
            iconSize: 24,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: color,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// Builds empty state for image area
  Widget _buildEmptyImageArea(ThemeData theme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.image_outlined,
            size: 64,
            color: theme.hintColor.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'Generated Image Will Appear Here',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.hintColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Configure your settings below and click Generate',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.hintColor.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Copy image to clipboard
  void _copyImageToClipboard(BuildContext context) {
    if (generator.generateImage != null) {
      Clipboard.setData(
        ClipboardData(text: base64Encode(generator.generateImage!)),
      );
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.white),
              SizedBox(width: 8),
              Text('Image copied to clipboard'),
            ],
          ),
          backgroundColor: AppTheme.primaryGreen,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  /// Share image
  void _shareImage(BuildContext context) async {
    if (generator.generateImage != null) {
      try {
        final directory = await getApplicationDocumentsDirectory();
        final imagePath = '${directory.path}/generated_image.png';
        final imageFile = File(imagePath);
        await imageFile.writeAsBytes(generator.generateImage!);
        Share.shareXFiles([
          XFile(imagePath),
        ], text: 'Check out this AI-generated image!');
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to share image: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  /// Save image to device
  void _saveImage(BuildContext context) async {
    // TODO: Implement save functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.info, color: Colors.white),
            SizedBox(width: 8),
            Text('Save functionality coming soon!'),
          ],
        ),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Builds enhanced controls section with modern styling
  Widget _buildControlsSection(BuildContext context, ThemeData theme) {
    return Column(
      children: [
        // Model Management Card
        if (controller.models == null)
          _buildLoadingCard(theme, 'Loading models...'),
        if (controller.models != null && controller.models!.isEmpty)
          _buildInfoCard(
            theme,
            'No models available',
            'Download models to start generating images',
            Icons.download,
          ),
        if (controller.models != null && controller.models!.isNotEmpty)
          _buildModelSelectionCard(context, theme),

        const SizedBox(height: 16),

        // Download Progress Card
        if (downloader.downloading || downloader.unziping)
          _buildProgressCard(context, theme),

        const SizedBox(height: 16),

        // Generation Controls Card
        if (loader.loaded) _buildGenerationCard(context, theme),

        // Loading Card
        if (loader.loading) _buildLoadingCard(theme, 'Loading model...'),
      ],
    );
  }

  /// Builds model selection card
  Widget _buildModelSelectionCard(BuildContext context, ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.memory, color: AppTheme.primaryGreen),
                const SizedBox(width: 8),
                Text(
                  'Model Selection',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryGreen,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Model Dropdown
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: theme.dividerColor),
                borderRadius: BorderRadius.circular(12),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: controller.selectedModel?.id,
                  isExpanded: true,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  items: controller.models?.map((model) {
                    return DropdownMenuItem<String>(
                      value: model.id,
                      child: FutureBuilder<bool>(
                        future: modelService.isDownloaded(model),
                        builder: (context, snapshot) {
                          Widget statusIcon;
                          Color statusColor;
                          String statusText;

                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            statusIcon = SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            );
                            statusColor = Colors.grey;
                            statusText = 'Checking...';
                          } else if (snapshot.hasData && snapshot.data!) {
                            statusIcon = Icon(Icons.check_circle, size: 20);
                            statusColor = Colors.green;
                            statusText = 'Downloaded';
                          } else {
                            statusIcon = Icon(Icons.download, size: 20);
                            statusColor = Colors.orange;
                            statusText = 'Not Downloaded';
                          }

                          return Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color: statusColor.withValues(alpha: 0.1),
                                  shape: BoxShape.circle,
                                ),
                                child: statusIcon,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      model.id,
                                      style: theme.textTheme.bodyMedium
                                          ?.copyWith(
                                            fontWeight: FontWeight.w500,
                                          ),
                                    ),
                                    Text(
                                      statusText,
                                      style: theme.textTheme.bodySmall
                                          ?.copyWith(color: statusColor),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    setState(() {
                      controller.selectedModel = controller.models!.firstWhere(
                        (model) => model.id == newValue,
                      );
                    });
                  },
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Action Buttons Row
            Row(
              children: [
                // Download Button
                if (controller.selectedModel != null)
                  FutureBuilder<bool>(
                    future: modelService.isDownloaded(
                      controller.selectedModel!,
                    ),
                    builder: (context, snapshot) {
                      final isDownloaded = snapshot.data ?? false;

                      if (!isDownloaded) {
                        return Expanded(
                          child: ElevatedButton.icon(
                            onPressed: controller.tapDownload,
                            icon: Icon(Icons.download, color: Colors.white),
                            label: Text(
                              'Download Model',
                              style: TextStyle(color: Colors.white),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.primaryGreen,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        );
                      }

                      return Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _showDeleteConfirmation(context),
                          icon: Icon(Icons.delete, color: Colors.white),
                          label: Text(
                            'Delete Model',
                            style: TextStyle(color: Colors.white),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Builds loading card
  Widget _buildLoadingCard(ThemeData theme, String message) {
    return Container(
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Row(
          children: [
            SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppTheme.primaryGreen,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.primaryGreen,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds info card
  Widget _buildInfoCard(
    ThemeData theme,
    String title,
    String subtitle,
    IconData icon,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Icon(icon, size: 48, color: theme.hintColor.withValues(alpha: 0.6)),
            const SizedBox(height: 16),
            Text(
              title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.hintColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Builds progress card for downloads
  Widget _buildProgressCard(BuildContext context, ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  downloader.downloading ? Icons.download : Icons.archive,
                  color: AppTheme.primaryGreen,
                ),
                const SizedBox(width: 8),
                Text(
                  downloader.downloading
                      ? 'Downloading Model'
                      : 'Extracting Model',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryGreen,
                  ),
                ),
                const Spacer(),
                if (!downloader.cancelDownloading && !downloader.canceUnziping)
                  IconButton(
                    onPressed: downloader.cancel,
                    icon: Icon(Icons.close, color: Colors.red),
                    tooltip: 'Cancel',
                  ),
              ],
            ),
            const SizedBox(height: 16),

            if (downloader.downloading) ...[
              Text(
                downloader.downloadProgressText ?? 'Preparing download...',
                style: theme.textTheme.bodyMedium,
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: downloader.progress / 100,
                backgroundColor: theme.dividerColor,
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppTheme.primaryGreen,
                ),
              ),
            ],

            if (downloader.unziping) ...[
              Text(
                downloader.unzipProgressText ?? 'Extracting files...',
                style: theme.textTheme.bodyMedium,
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                backgroundColor: theme.dividerColor,
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppTheme.primaryGreen,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Builds generation controls card
  Widget _buildGenerationCard(BuildContext context, ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.auto_awesome, color: AppTheme.primaryGreen),
                const SizedBox(width: 8),
                Text(
                  'Generate Image',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryGreen,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Input widgets
            buildInputWidgets(context),

            const SizedBox(height: 20),

            // Generation buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed:
                        !controller.generator.generateing &&
                            !controller.generator.cancelGenerateing
                        ? () {
                            // Update enhanced prompt before generating
                            _updateEnhancedPromptPreview();
                            // Pass the enhanced prompt to the controller
                            controller.tapGenerate(
                              enhancedPrompt: _enhancedPromptPreview.isNotEmpty
                                  ? _enhancedPromptPreview
                                  : null,
                            );
                          }
                        : null,
                    icon: Icon(Icons.auto_awesome, color: Colors.white),
                    label: Text(
                      'Generate',
                      style: TextStyle(color: Colors.white),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryGreen,
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed:
                        controller.generator.generateing &&
                            !controller.generator.cancelGenerateing
                        ? controller.generator.cancel
                        : null,
                    icon: Icon(Icons.stop, color: Colors.red),
                    label: Text('Cancel', style: TextStyle(color: Colors.red)),
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: Colors.red),
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            // Progress info
            if (controller.generator.generateing) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      controller.generator.generateProgressText ??
                          'Generating...',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: AppTheme.primaryGreen,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (controller.generator.generateTimingText != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        'Time: ${controller.generator.generateTimingText}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: AppTheme.primaryGreen.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Shows delete confirmation dialog
  void _showDeleteConfirmation(BuildContext context) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.red),
            const SizedBox(width: 8),
            Text('Delete Model'),
          ],
        ),
        content: Text(
          'Are you sure you want to delete this model? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text('Delete', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed == true && controller.selectedModel != null) {
      await modelService.deleteModel(controller.selectedModel!);
      setState(() {
        controller.selectedModel = null;
      });
    }
  }
}
