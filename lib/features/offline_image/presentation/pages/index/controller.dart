import 'dart:async';

import 'package:bot_toast/bot_toast.dart';
import 'package:diogeneschatbot/features/offline_image/data/local/data_sources/prompt.dart';
import 'package:diogeneschatbot/models/model_info.dart';
import 'package:diogeneschatbot/features/offline_image/domain/controller/model_service.dart';
import 'package:diogeneschatbot/features/offline_image/presentation/pages/dialogs/select_dialog.dart';
import 'package:diogeneschatbot/utils/logger.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

import 'downloader.dart';
import 'generator.dart';
import 'loader.dart';

class IndexPageController with ChangeNotifier {
  final ModelService modelService = GetIt.instance<ModelService>();
  final IndexPageModelDownloader downloader =
      GetIt.instance<IndexPageModelDownloader>();
  final IndexPageModelLoader loader = GetIt.instance<IndexPageModelLoader>();
  final IndexPageGenerator generator = GetIt.instance<IndexPageGenerator>();

  late final TextEditingController promptTextController =
      TextEditingController();
  late final TextEditingController negativePromptTextController =
      TextEditingController(text: Prompt.default_negative_prompt);
  late final TextEditingController stepCountTextController =
      TextEditingController(text: "20");
  late final TextEditingController scaleTextController = TextEditingController(
    text: "7.5",
  );

  List<ModelInfo>? models;
  ModelInfo? selectedModel;
  String? errorMessage;
  bool switchingModel = false;

  // Add a ValueNotifier for models
  ValueNotifier<List<ModelInfo>?> modelsNotifier =
      ValueNotifier<List<ModelInfo>?>(null);

  Future<void> _getModels() async {
    models = await modelService.getModels();
    modelsNotifier.value = models; // Update the ValueNotifier
    if (models?.isNotEmpty == true) {
      switchModel(models!.first);
    } else {
      modelsNotifier.value = null; // Set to null if no models found
    }
    notifyListeners();
  }

  void switchModel(ModelInfo model) async {
    try {
      logger.i("Switching model to: ${model.id}"); // Logging the switch
      selectedModel = model;
      switchingModel = true;
      updateError();

      // Notify the user about the model switch
      BotToast.showText(text: "Switching to model: ${model.id}");

      await Future.wait([
        generator.cancel(),
        downloader.switchModel(model),
        loader.switchModel(model),
      ]);

      switchingModel = false;
      notifyListeners();

      if (downloader.downloaded) {
        await loader.load();
      }
      notifyListeners();
    } catch (e) {
      logger.e('Error message: $e');
      BotToast.showText(text: 'An error occurred: ${e.toString()}');
    }
  }

  void tapSelectModel(BuildContext context) async {
    var model = await showSelectModelDialog(context, models ?? []);
    if (model != null && selectedModel != model) {
      switchModel(model);
    }
  }

  void tapDownload() async {
    await downloader.download();
    updateError();
    if (downloader.downloaded) {
      await loader.load();
    }
  }

  void tapGenerate({String? enhancedPrompt}) async {
    updateError();
    var stepCount = int.tryParse(stepCountTextController.text);
    if (stepCount == null) {
      errorMessage = "Step count must be a number.";
      notifyListeners();
      return;
    }
    var scale = double.tryParse(scaleTextController.text);
    if (scale == null) {
      errorMessage = "Guidance scale must be a number.";
      notifyListeners();
      return;
    }

    // Use enhanced prompt if provided, otherwise use the original prompt
    final promptToUse = enhancedPrompt ?? promptTextController.text;

    await generator.generate(
      promptToUse,
      negativePromptTextController.text,
      stepCount,
      scale: scale,
    );
  }

  void updateError([String? error]) {
    errorMessage = error;
    notifyListeners();
  }

  Future<void> init() async {
    await _getModels();
    modelsNotifier.notifyListeners();
    notifyListeners();
  }
}
