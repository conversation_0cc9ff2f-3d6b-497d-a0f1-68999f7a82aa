import 'package:flutter/material.dart';
import 'package:diogeneschatbot/features/image_generation/models/image_generation_settings.dart';
import 'package:diogeneschatbot/features/image_generation/models/image_style_presets.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';

/// Reusable widget for image generation style settings
///
/// This widget provides a consistent interface for selecting style categories,
/// specific styles, lighting moods, and other generation parameters that can
/// be used by both OpenAI and local image generation features.
class StyleSettingsWidget extends StatelessWidget {
  final ImageGenerationSettings settings;
  final ValueChanged<ImageGenerationSettings> onSettingsChanged;
  final bool showAdvancedOptions;
  final bool compact;

  const StyleSettingsWidget({
    super.key,
    required this.settings,
    required this.onSettingsChanged,
    this.showAdvancedOptions = true,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Style Category Selection
        _buildSectionTitle('Style Category'),
        const SizedBox(height: 8),
        _buildDropdown<StyleCategory>(
          context,
          value: settings.styleCategory,
          items: StyleCategory.values,
          itemBuilder: (category) => _getStyleCategoryName(category),
          onChanged: (StyleCategory? newValue) {
            if (newValue != null) {
              onSettingsChanged(
                settings.copyWith(
                  styleCategory: newValue,
                  selectedStyle:
                      null, // Reset specific style when category changes
                ),
              );
            }
          },
          hint: 'Choose a style category',
        ),

        const SizedBox(height: 16),

        // Specific Style Selection
        if (settings.styleCategory != null) ...[
          _buildSectionTitle('Specific Style'),
          const SizedBox(height: 8),
          _buildDropdown<String>(
            context,
            value: settings.selectedStyle,
            items: ImageStylePresets.getStylesForCategory(
              settings.styleCategory!,
            ),
            itemBuilder: (style) => style,
            onChanged: (String? newValue) {
              onSettingsChanged(settings.copyWith(selectedStyle: newValue));
            },
            hint: 'Choose a specific style (optional)',
          ),
          const SizedBox(height: 16),
        ],

        // Advanced Options
        if (showAdvancedOptions) ...[
          // Lighting Mood
          _buildSectionTitle('Lighting & Mood'),
          const SizedBox(height: 8),
          _buildDropdown<LightingMood>(
            context,
            value: settings.lightingMood,
            items: LightingMood.values,
            itemBuilder: (mood) => _getLightingMoodName(mood),
            onChanged: (LightingMood? newValue) {
              onSettingsChanged(settings.copyWith(lightingMood: newValue));
            },
            hint: 'Choose lighting mood (optional)',
          ),

          const SizedBox(height: 16),

          // Composition Guide
          _buildSectionTitle('Composition'),
          const SizedBox(height: 8),
          _buildDropdown<CompositionGuide>(
            context,
            value: settings.compositionGuide,
            items: CompositionGuide.values,
            itemBuilder: (guide) => _getCompositionGuideName(guide),
            onChanged: (CompositionGuide? newValue) {
              onSettingsChanged(settings.copyWith(compositionGuide: newValue));
            },
            hint: 'Choose composition guide (optional)',
          ),

          const SizedBox(height: 16),

          // Advanced Prompting Toggle
          CheckboxListTile(
            title: const Text('Use Advanced Prompting'),
            subtitle: const Text('Adds quality enhancers to the prompt'),
            value: settings.useAdvancedPrompting,
            onChanged: (bool? value) {
              onSettingsChanged(
                settings.copyWith(useAdvancedPrompting: value ?? false),
              );
            },
            activeColor: AppTheme.primaryGreen,
            contentPadding: EdgeInsets.zero,
          ),
        ],
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontWeight: FontWeight.bold,
        fontSize: compact ? 16 : 18,
      ),
    );
  }

  Widget _buildDropdown<T>(
    BuildContext context, {
    required T? value,
    required List<T> items,
    required String Function(T) itemBuilder,
    required ValueChanged<T?> onChanged,
    required String hint,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Theme.of(context).dividerColor),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<T>(
          value: value,
          isExpanded: true,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          hint: Text(hint),
          items: items.map((item) {
            return DropdownMenuItem<T>(
              value: item,
              child: Text(itemBuilder(item)),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ),
    );
  }

  String _getStyleCategoryName(StyleCategory category) {
    switch (category) {
      case StyleCategory.photorealistic:
        return 'Photorealistic';
      case StyleCategory.artistic:
        return 'Artistic';
      case StyleCategory.technical:
        return 'Technical';
      case StyleCategory.creative:
        return 'Creative';
    }
  }

  String _getLightingMoodName(LightingMood mood) {
    switch (mood) {
      case LightingMood.dramatic:
        return 'Dramatic';
      case LightingMood.soft:
        return 'Soft';
      case LightingMood.neon:
        return 'Neon';
      case LightingMood.vintage:
        return 'Vintage';
      case LightingMood.natural:
        return 'Natural';
      case LightingMood.cinematic:
        return 'Cinematic';
      case LightingMood.golden:
        return 'Golden Hour';
      case LightingMood.blue:
        return 'Blue Hour';
    }
  }

  String _getCompositionGuideName(CompositionGuide guide) {
    switch (guide) {
      case CompositionGuide.ruleOfThirds:
        return 'Rule of Thirds';
      case CompositionGuide.goldenRatio:
        return 'Golden Ratio';
      case CompositionGuide.centered:
        return 'Centered';
      case CompositionGuide.leadingLines:
        return 'Leading Lines';
      case CompositionGuide.symmetrical:
        return 'Symmetrical';
      case CompositionGuide.asymmetrical:
        return 'Asymmetrical';
    }
  }
}
