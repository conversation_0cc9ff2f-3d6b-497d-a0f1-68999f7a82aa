import 'dart:io';
import 'dart:math';

import 'package:diogeneschatbot/features/image_generation/models/image_generation_settings.dart';
import 'package:diogeneschatbot/features/image_generation/models/image_style_presets.dart';
import 'package:diogeneschatbot/features/image_generation/services/openai_image_service.dart';
import 'package:diogeneschatbot/models/usage.dart';
import 'package:diogeneschatbot/util/util_api_usage.dart';
import 'package:diogeneschatbot/utils/logger.dart';

/// Main service for image generation operations
///
/// Coordinates between the UI and OpenAI service, handles prompt enhancement,
/// and manages the overall image generation workflow.
class ImageGenerationService {
  // Private constructor to prevent instantiation
  ImageGenerationService._();

  /// Generates a unique response ID for image generation operations
  ///
  /// Since OpenAI image API doesn't return response IDs like chat API,
  /// we generate our own for tracking purposes.
  static String _generateResponseId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(999999);
    return 'img-gen-$timestamp-$random';
  }

  /// Generates an image with the given prompt and settings
  ///
  /// Enhances the prompt with style settings and calls the OpenAI service
  /// to generate the image. Handles all error cases and logging.
  ///
  /// [prompt] The user's original prompt
  /// [settings] Image generation settings for style enhancement
  /// [userId] Current user ID for tracking
  /// [usageType] Usage type for limit checking
  ///
  /// Returns the Firebase URL of the generated image
  static Future<String> generateImage(
    String prompt,
    ImageGenerationSettings settings,
    String userId,
    Usage usageType,
  ) async {
    if (prompt.trim().isEmpty) {
      throw ArgumentError('Prompt cannot be empty');
    }

    try {
      // Build enhanced prompt with style settings
      final enhancedPrompt = ImageStylePresets.buildEnhancedPrompt(
        prompt.trim(),
        settings,
      );

      logger.d('Generating image with enhanced prompt: $enhancedPrompt');

      // Call OpenAI service to generate the image
      final imageUrl = await OpenAIImageService.generateImage(
        enhancedPrompt,
        settings,
        userId,
        usageType,
      );

      // Generate a unique response ID for tracking
      final responseId = _generateResponseId();

      // Save API usage for tracking
      await Util_API_Usage.saveApiUsage(
        responseId: responseId,
        apiUrl: 'https://api.openai.com/v1/images/generations',
        usageType: UsageType.generateImage.toString(),
        questionContent: enhancedPrompt,
        responseMessage: imageUrl,
        currentUserId: userId,
        conversationId: userId,
        question: prompt,
      );

      return imageUrl;
    } catch (e) {
      logger.e('Image generation failed: $e');

      // Provide user-friendly error messages
      if (e.toString().contains('Usage limit')) {
        throw Exception(
          'Usage limit exceeded. Please try again later or upgrade your plan.',
        );
      } else if (e.toString().contains('API key')) {
        throw Exception('API configuration error. Please contact support.');
      } else if (e.toString().contains('content policy')) {
        throw Exception(
          'Content violates OpenAI policy. Please modify your prompt.',
        );
      } else {
        throw Exception('Failed to generate image. Please try again.');
      }
    }
  }

  /// Edits an existing image with AI
  ///
  /// Enhances the editing prompt and calls the OpenAI service to edit the image.
  ///
  /// [image] The source image to edit
  /// [mask] Optional mask image for selective editing
  /// [prompt] The editing instruction
  /// [settings] Image generation settings for style enhancement
  /// [userId] Current user ID for tracking
  /// [usageType] Usage type for limit checking
  ///
  /// Returns the Firebase URL of the edited image
  static Future<String> editImage(
    File image,
    File? mask,
    String prompt,
    ImageGenerationSettings settings,
    String userId,
    Usage usageType,
  ) async {
    if (prompt.trim().isEmpty) {
      throw ArgumentError('Editing prompt cannot be empty');
    }

    try {
      // Build enhanced prompt for editing
      final enhancedPrompt = ImageStylePresets.buildEnhancedPrompt(
        prompt.trim(),
        settings,
      );

      logger.d('Editing image with enhanced prompt: $enhancedPrompt');

      // Call OpenAI service to edit the image
      final imageUrl = await OpenAIImageService.editImage(
        image,
        mask,
        enhancedPrompt,
        userId,
        usageType,
      );

      // Generate a unique response ID for tracking
      final responseId = _generateResponseId();

      // Save API usage for tracking
      await Util_API_Usage.saveApiUsage(
        responseId: responseId,
        apiUrl: 'https://api.openai.com/v1/images/edits',
        usageType: UsageType.generateImage.toString(),
        questionContent: enhancedPrompt,
        responseMessage: imageUrl,
        currentUserId: userId,
        conversationId: userId,
        question: prompt,
      );

      return imageUrl;
    } catch (e) {
      logger.e('Image editing failed: $e');

      // Provide user-friendly error messages
      if (e.toString().contains('Usage limit')) {
        throw Exception(
          'Usage limit exceeded. Please try again later or upgrade your plan.',
        );
      } else if (e.toString().contains('API key')) {
        throw Exception('API configuration error. Please contact support.');
      } else {
        throw Exception('Failed to edit image. Please try again.');
      }
    }
  }

  /// Creates variations of an existing image
  ///
  /// Calls the OpenAI service to create variations of the provided image.
  ///
  /// [image] The source image to create variations from
  /// [userId] Current user ID for tracking
  /// [usageType] Usage type for limit checking
  ///
  /// Returns the Firebase URL of the variation image
  static Future<String> createVariations(
    File image,
    String userId,
    Usage usageType,
  ) async {
    try {
      logger.d('Creating image variations');

      // Call OpenAI service to create variations
      final imageUrl = await OpenAIImageService.createVariations(
        image,
        userId,
        usageType,
      );

      // Generate a unique response ID for tracking
      final responseId = _generateResponseId();

      // Save API usage for tracking
      await Util_API_Usage.saveApiUsage(
        responseId: responseId,
        apiUrl: 'https://api.openai.com/v1/images/variations',
        usageType: UsageType.generateImage.toString(),
        questionContent: "",
        responseMessage: imageUrl,
        currentUserId: userId,
        conversationId: userId,
        question: "Image variations",
      );

      return imageUrl;
    } catch (e) {
      logger.e('Image variation creation failed: $e');

      // Provide user-friendly error messages
      if (e.toString().contains('Usage limit')) {
        throw Exception(
          'Usage limit exceeded. Please try again later or upgrade your plan.',
        );
      } else if (e.toString().contains('API key')) {
        throw Exception('API configuration error. Please contact support.');
      } else {
        throw Exception('Failed to create variations. Please try again.');
      }
    }
  }

  /// Gets a preview of how the prompt will be enhanced with current settings
  ///
  /// Useful for showing users how their style settings affect the final prompt.
  ///
  /// [basePrompt] The user's original prompt
  /// [settings] Current image generation settings
  ///
  /// Returns the enhanced prompt that would be sent to the API
  static String getEnhancedPromptPreview(
    String basePrompt,
    ImageGenerationSettings settings,
  ) {
    if (basePrompt.trim().isEmpty) {
      return 'Enter a prompt to see the enhanced version...';
    }

    try {
      return ImageStylePresets.buildEnhancedPrompt(basePrompt.trim(), settings);
    } catch (e) {
      logger.w('Error building prompt preview: $e');
      return basePrompt.trim();
    }
  }

  /// Validates if an image generation request can be made
  ///
  /// Checks usage limits and prompt validity before attempting generation.
  ///
  /// [prompt] The user's prompt
  /// [userId] Current user ID
  /// [usageType] Usage type for limit checking
  ///
  /// Returns true if the request can proceed, throws exception otherwise
  static Future<bool> validateGenerationRequest(
    String prompt,
    String userId,
    Usage usageType,
  ) async {
    if (prompt.trim().isEmpty) {
      throw ArgumentError('Please enter a description for the image.');
    }

    if (await Util_API_Usage.isLimitExceeded(usageType, userId)) {
      throw Exception(
        'Usage limit exceeded. Please try again later or upgrade your plan.',
      );
    }

    return true;
  }

  /// Validates if an image editing request can be made
  ///
  /// Checks usage limits, image validity, and prompt before attempting editing.
  ///
  /// [image] The source image to edit
  /// [prompt] The editing instruction
  /// [userId] Current user ID
  /// [usageType] Usage type for limit checking
  ///
  /// Returns true if the request can proceed, throws exception otherwise
  static Future<bool> validateEditingRequest(
    File? image,
    String prompt,
    String userId,
    Usage usageType,
  ) async {
    if (image == null) {
      throw ArgumentError('Please upload an image first.');
    }

    if (prompt.trim().isEmpty) {
      throw ArgumentError('Please describe how you want to edit the image.');
    }

    if (await Util_API_Usage.isLimitExceeded(usageType, userId)) {
      throw Exception(
        'Usage limit exceeded. Please try again later or upgrade your plan.',
      );
    }

    return true;
  }

  /// Validates if an image variation request can be made
  ///
  /// Checks usage limits and image validity before attempting variation creation.
  ///
  /// [image] The source image to create variations from
  /// [userId] Current user ID
  /// [usageType] Usage type for limit checking
  ///
  /// Returns true if the request can proceed, throws exception otherwise
  static Future<bool> validateVariationRequest(
    File? image,
    String userId,
    Usage usageType,
  ) async {
    if (image == null) {
      throw ArgumentError('Please upload an image first.');
    }

    if (await Util_API_Usage.isLimitExceeded(usageType, userId)) {
      throw Exception(
        'Usage limit exceeded. Please try again later or upgrade your plan.',
      );
    }

    return true;
  }
}
