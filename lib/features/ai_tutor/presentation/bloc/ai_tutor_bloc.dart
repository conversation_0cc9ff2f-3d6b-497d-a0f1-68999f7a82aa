import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../domain/entities/flashcard.dart';
import '../../domain/entities/quiz.dart';
import '../../domain/entities/learning_progress.dart';
import '../../domain/entities/learning_session.dart';
import '../../domain/repositories/ai_tutor_repository.dart';
import '../../domain/use_cases/generate_learning_plan_use_case.dart';
import '../../domain/use_cases/create_flashcards_use_case.dart';
import '../../domain/use_cases/track_progress_use_case.dart';
import '../../domain/use_cases/generate_quiz_use_case.dart';
import '../../domain/use_cases/explain_concept_use_case.dart';
import '../../domain/use_cases/identify_knowledge_gaps_use_case.dart';
import '../../domain/use_cases/load_study_recommendations_use_case.dart';

part 'ai_tutor_event.dart';
part 'ai_tutor_state.dart';

/// BLoC for managing AI Tutor functionality
class AITutorBloc extends Bloc<AITutorEvent, AITutorState> {
  final GenerateLearningPlanUseCase _generateLearningPlan;
  final CreateFlashcardsUseCase _createFlashcards;
  final TrackProgressUseCase _trackProgress;
  final GenerateQuizUseCase _generateQuiz;
  final ExplainConceptUseCase _explainConcept;
  final IdentifyKnowledgeGapsUseCase _identifyKnowledgeGaps;
  final LoadStudyRecommendationsUseCase _loadStudyRecommendations;

  AITutorBloc({
    required GenerateLearningPlanUseCase generateLearningPlan,
    required CreateFlashcardsUseCase createFlashcards,
    required TrackProgressUseCase trackProgress,
    required GenerateQuizUseCase generateQuiz,
    required ExplainConceptUseCase explainConcept,
    required IdentifyKnowledgeGapsUseCase identifyKnowledgeGaps,
    required LoadStudyRecommendationsUseCase loadStudyRecommendations,
  }) : _generateLearningPlan = generateLearningPlan,
       _createFlashcards = createFlashcards,
       _trackProgress = trackProgress,
       _generateQuiz = generateQuiz,
       _explainConcept = explainConcept,
       _identifyKnowledgeGaps = identifyKnowledgeGaps,
       _loadStudyRecommendations = loadStudyRecommendations,
       super(const AITutorInitial()) {
    on<GenerateLearningPlanEvent>(_onGenerateLearningPlan);
    on<CreateFlashcardsEvent>(_onCreateFlashcards);
    on<GenerateQuizEvent>(_onGenerateQuiz);
    on<StartLearningSessionEvent>(_onStartLearningSession);
    on<EndLearningSessionEvent>(_onEndLearningSession);
    on<LoadLearningProgressEvent>(_onLoadLearningProgress);
    on<UpdateLearningProgressEvent>(_onUpdateLearningProgress);
    on<LoadLearningPlansEvent>(_onLoadLearningPlans);
    on<SaveLearningPlanEvent>(_onSaveLearningPlan);
    on<ExplainConceptEvent>(_onExplainConcept);
    on<IdentifyKnowledgeGapsEvent>(_onIdentifyKnowledgeGaps);
    on<LoadStudyRecommendationsEvent>(_onLoadStudyRecommendations);
    on<ResetAITutorEvent>(_onResetAITutor);
  }

  Future<void> _onGenerateLearningPlan(
    GenerateLearningPlanEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(
      const AITutorLoading(
        message: 'Generating your personalized learning plan...',
      ),
    );

    final result = await _generateLearningPlan(
      GenerateLearningPlanParams(
        subject: event.subject,
        currentLevel: event.currentLevel,
        learningGoals: event.learningGoals,
        preferences: event.preferences,
      ),
    );

    result.fold(
      (failure) => emit(AITutorError(failure.message)),
      (learningPlan) => emit(LearningPlanGenerated(learningPlan)),
    );
  }

  Future<void> _onCreateFlashcards(
    CreateFlashcardsEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Creating flashcards...'));

    final result = await _createFlashcards(
      CreateFlashcardsParams(
        topic: event.topic,
        count: event.count,
        difficulty: event.difficulty,
        context: event.context,
      ),
    );

    result.fold(
      (failure) => emit(AITutorError(failure.message)),
      (flashcards) => emit(FlashcardsCreated(flashcards)),
    );
  }

  Future<void> _onGenerateQuiz(
    GenerateQuizEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Generating adaptive quiz...'));

    final result = await _generateQuiz(
      GenerateQuizParams(
        topic: event.topic,
        concepts: event.concepts,
        difficulty: event.difficulty,
        previousResults: event.previousResults,
      ),
    );

    result.fold(
      (failure) => emit(AITutorError(failure.message)),
      (quiz) => emit(QuizGenerated(quiz)),
    );
  }

  Future<void> _onStartLearningSession(
    StartLearningSessionEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Starting learning session...'));

    // TODO: Implement proper session start logic with persistence
    // TODO: Validate session parameters and user authentication
    final session = LearningSession(
      id: 'session_${DateTime.now().millisecondsSinceEpoch}',
      userId: FirebaseAuth.instance.currentUser?.uid ?? 'anonymous',
      subject: event.subject,
      topic: event.topic,
      startTime: DateTime.now(),
      status: LearningSessionStatus.active,
      conceptsCovered: event.conceptsCovered,
      comprehensionScore: 0.0, // TODO: Initialize with baseline assessment
      metadata:
          {}, // TODO: Add session metadata like difficulty level, preferences
    );

    // TODO: Save session to repository/database
    emit(LearningSessionStarted(session));
  }

  Future<void> _onEndLearningSession(
    EndLearningSessionEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Ending learning session...'));

    // TODO: Implement session end logic
    await Future.delayed(const Duration(seconds: 1));

    // Mock session for now
    final session = LearningSession(
      id: event.sessionId,
      userId: 'current_user',
      subject: 'Mathematics',
      topic: 'Algebra',
      startTime: DateTime.now().subtract(const Duration(minutes: 30)),
      endTime: DateTime.now(),
      status: LearningSessionStatus.completed,
      conceptsCovered: ['Linear Equations', 'Quadratic Equations'],
      comprehensionScore: event.comprehensionScore,
      metadata: event.metadata,
    );

    emit(LearningSessionEnded(session));
  }

  Future<void> _onLoadLearningProgress(
    LoadLearningProgressEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Loading learning progress...'));

    final result = await _trackProgress(
      TrackProgressParams(
        userId: event.userId,
        subject: event.subject,
        topic: event.topic,
      ),
    );

    result.fold(
      (failure) => emit(AITutorError(failure.message)),
      (progress) => emit(LearningProgressLoaded(progress)),
    );
  }

  Future<void> _onUpdateLearningProgress(
    UpdateLearningProgressEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Updating progress...'));

    // TODO: Implement progress update logic
    await Future.delayed(const Duration(seconds: 1));

    emit(LearningProgressUpdated(event.progress));
  }

  Future<void> _onLoadLearningPlans(
    LoadLearningPlansEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Loading learning plans...'));

    // TODO: Implement learning plans loading
    await Future.delayed(const Duration(seconds: 1));

    emit(const LearningPlansLoaded([])); // Empty list for now
  }

  Future<void> _onSaveLearningPlan(
    SaveLearningPlanEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Saving learning plan...'));

    // TODO: Implement learning plan saving
    await Future.delayed(const Duration(seconds: 1));

    emit(LearningPlanSaved(event.plan));
  }

  Future<void> _onExplainConcept(
    ExplainConceptEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Generating explanation...'));

    final result = await _explainConcept(
      ExplainConceptParams(
        concept: event.concept,
        context: event.context,
        style: event.style,
      ),
    );

    result.fold(
      (failure) => emit(AITutorError(failure.message)),
      (explanation) => emit(
        ConceptExplained(
          concept: event.concept,
          explanation: explanation,
          style: event.style,
        ),
      ),
    );
  }

  Future<void> _onIdentifyKnowledgeGaps(
    IdentifyKnowledgeGapsEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Analyzing knowledge gaps...'));

    final result = await _identifyKnowledgeGaps(
      IdentifyKnowledgeGapsParams(
        quizResults: event.quizResults,
        subject: event.subject,
      ),
    );

    result.fold(
      (failure) => emit(AITutorError(failure.message)),
      (knowledgeGaps) => emit(
        KnowledgeGapsIdentified(
          knowledgeGaps: knowledgeGaps,
          subject: event.subject,
        ),
      ),
    );
  }

  Future<void> _onLoadStudyRecommendations(
    LoadStudyRecommendationsEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Loading study recommendations...'));

    final result = await _loadStudyRecommendations(
      LoadStudyRecommendationsParams(
        userId: event.userId,
        subject: event.subject,
      ),
    );

    result.fold(
      (failure) => emit(AITutorError(failure.message)),
      (recommendations) => emit(StudyRecommendationsLoaded(recommendations)),
    );
  }

  Future<void> _onResetAITutor(
    ResetAITutorEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorInitial());
  }
}
