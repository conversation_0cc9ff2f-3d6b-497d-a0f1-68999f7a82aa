part of 'ai_tutor_bloc.dart';

/// Base class for all AI Tutor states
abstract class AITutorState extends Equatable {
  const AITutorState();

  @override
  List<Object?> get props => [];
}

/// Initial state of the AI Tutor
class AIT<PERSON>rInitial extends AITutorState {
  const AITutorInitial();

  @override
  String toString() => 'AITutorInitial()';
}

/// Loading state for AI Tutor operations
class AITutorLoading extends AITutorState {
  final String? message;

  const AITutorLoading({this.message});

  @override
  List<Object?> get props => [message];

  @override
  String toString() => 'AITutorLoading(message: $message)';
}

/// Error state for AI Tutor operations
class AITutorError extends AITutorState {
  final String message;
  final String? code;

  const AITutorError(this.message, {this.code});

  @override
  List<Object?> get props => [message, code];

  @override
  String toString() => 'AITutorError(message: $message, code: $code)';
}

/// State when a learning plan has been generated
class LearningPlanGenerated extends AITutorState {
  final LearningPlan learningPlan;

  const LearningPlanGenerated(this.learningPlan);

  @override
  List<Object> get props => [learningPlan];

  @override
  String toString() => 'LearningPlanGenerated(plan: $learningPlan)';
}

/// State when learning plans have been loaded
class LearningPlansLoaded extends AITutorState {
  final List<LearningPlan> learningPlans;

  const LearningPlansLoaded(this.learningPlans);

  @override
  List<Object> get props => [learningPlans];

  @override
  String toString() => 'LearningPlansLoaded(count: ${learningPlans.length})';
}

/// State when flashcards have been created
class FlashcardsCreated extends AITutorState {
  final List<Flashcard> flashcards;

  const FlashcardsCreated(this.flashcards);

  @override
  List<Object> get props => [flashcards];

  @override
  String toString() => 'FlashcardsCreated(count: ${flashcards.length})';
}

/// State when a quiz has been generated
class QuizGenerated extends AITutorState {
  final Quiz quiz;

  const QuizGenerated(this.quiz);

  @override
  List<Object> get props => [quiz];

  @override
  String toString() => 'QuizGenerated(quiz: $quiz)';
}

/// State when a learning session has been started
class LearningSessionStarted extends AITutorState {
  final LearningSession session;

  const LearningSessionStarted(this.session);

  @override
  List<Object> get props => [session];

  @override
  String toString() => 'LearningSessionStarted(session: $session)';
}

/// State when a learning session has been ended
class LearningSessionEnded extends AITutorState {
  final LearningSession session;

  const LearningSessionEnded(this.session);

  @override
  List<Object> get props => [session];

  @override
  String toString() => 'LearningSessionEnded(session: $session)';
}

/// State when learning progress has been loaded
class LearningProgressLoaded extends AITutorState {
  final LearningProgress? progress;

  const LearningProgressLoaded(this.progress);

  @override
  List<Object?> get props => [progress];

  @override
  String toString() => 'LearningProgressLoaded(progress: $progress)';
}

/// State when learning progress has been updated
class LearningProgressUpdated extends AITutorState {
  final LearningProgress progress;

  const LearningProgressUpdated(this.progress);

  @override
  List<Object> get props => [progress];

  @override
  String toString() => 'LearningProgressUpdated(progress: $progress)';
}

/// State when a concept has been explained
class ConceptExplained extends AITutorState {
  final String concept;
  final String explanation;
  final ExplanationStyle style;

  const ConceptExplained({
    required this.concept,
    required this.explanation,
    required this.style,
  });

  @override
  List<Object> get props => [concept, explanation, style];

  @override
  String toString() => 'ConceptExplained(concept: $concept, style: $style)';
}

/// State when knowledge gaps have been identified
class KnowledgeGapsIdentified extends AITutorState {
  final List<String> knowledgeGaps;
  final String subject;

  const KnowledgeGapsIdentified({
    required this.knowledgeGaps,
    required this.subject,
  });

  @override
  List<Object> get props => [knowledgeGaps, subject];

  @override
  String toString() => 'KnowledgeGapsIdentified(gaps: ${knowledgeGaps.length}, subject: $subject)';
}

/// State when study recommendations have been loaded
class StudyRecommendationsLoaded extends AITutorState {
  final List<StudyRecommendation> recommendations;

  const StudyRecommendationsLoaded(this.recommendations);

  @override
  List<Object> get props => [recommendations];

  @override
  String toString() => 'StudyRecommendationsLoaded(count: ${recommendations.length})';
}

/// State when a learning plan has been saved
class LearningPlanSaved extends AITutorState {
  final LearningPlan plan;

  const LearningPlanSaved(this.plan);

  @override
  List<Object> get props => [plan];

  @override
  String toString() => 'LearningPlanSaved(plan: $plan)';
}

/// Composite state that holds multiple pieces of data
class AITutorDataLoaded extends AITutorState {
  final List<LearningPlan>? learningPlans;
  final LearningProgress? progress;
  final List<StudyRecommendation>? recommendations;
  final List<Flashcard>? dueFlashcards;

  const AITutorDataLoaded({
    this.learningPlans,
    this.progress,
    this.recommendations,
    this.dueFlashcards,
  });

  @override
  List<Object?> get props => [learningPlans, progress, recommendations, dueFlashcards];

  @override
  String toString() {
    return 'AITutorDataLoaded(plans: ${learningPlans?.length}, progress: $progress, recommendations: ${recommendations?.length}, flashcards: ${dueFlashcards?.length})';
  }
}

/// State for successful operations without specific data
class AITutorSuccess extends AITutorState {
  final String message;

  const AITutorSuccess(this.message);

  @override
  List<Object> get props => [message];

  @override
  String toString() => 'AITutorSuccess(message: $message)';
}
