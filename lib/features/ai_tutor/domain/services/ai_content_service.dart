import 'package:langchain/langchain.dart';
import 'package:langchain_openai/langchain_openai.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../entities/flashcard.dart';
import '../entities/quiz.dart';
import '../entities/learning_progress.dart';
import '../repositories/ai_tutor_repository.dart';

/// Service for generating AI content using LangChain
class AIContentService {
  late final ChatOpenAI _chatModel;
  late final PromptTemplate _learningPlanTemplate;
  late final PromptTemplate _flashcardTemplate;
  late final PromptTemplate _quizTemplate;
  late final PromptTemplate _explanationTemplate;

  AIContentService({String? apiKey}) {
    // Get API key from dotenv environment or use provided key
    final effectiveApiKey =
        apiKey ??
        dotenv.env['OPENAI_API_KEY'] ??
        const String.fromEnvironment('OPENAI_API_KEY', defaultValue: '');

    if (effectiveApiKey.isEmpty) {
      throw Exception(
        'OpenAI API key is required. Set OPENAI_API_KEY environment variable or provide apiKey parameter.',
      );
    }

    _chatModel = ChatOpenAI(
      apiKey: effectiveApiKey,
      defaultOptions: const ChatOpenAIOptions(
        model: 'gpt-3.5-turbo',
        temperature: 0.7,
        maxTokens: 2000,
      ),
    );

    _initializePromptTemplates();
  }

  /// Initializes prompt templates for different AI content generation tasks
  void _initializePromptTemplates() {
    _learningPlanTemplate = PromptTemplate.fromTemplate('''
You are an expert educational AI tutor. Create a comprehensive learning plan for the following:

Subject: {subject}
Current Level: {currentLevel}
Learning Goals: {learningGoals}
Preferences: {preferences}

Generate a structured learning plan with:
1. Clear milestones (3-5 milestones)
2. Estimated timeframes
3. Learning resources
4. Assessment methods
5. Prerequisites for each milestone

Format the response as JSON with the following structure:
{{
  "title": "Learning Plan Title",
  "description": "Brief description",
  "milestones": [
    {{
      "title": "Milestone Title",
      "description": "Detailed description",
      "concepts": ["concept1", "concept2"],
      "estimatedDays": 14,
      "resources": ["resource1", "resource2"]
    }}
  ],
  "totalDuration": 60,
  "difficulty": "intermediate"
}}
''');

    _flashcardTemplate = PromptTemplate.fromTemplate('''
You are an expert educational content creator. Generate {count} flashcards for the topic: {topic}

Difficulty Level: {difficulty}
Context: {context}

Create flashcards that:
1. Test key concepts and understanding
2. Use clear, concise questions
3. Provide comprehensive answers
4. Include practical examples where relevant
5. Progress from basic to advanced concepts

Format each flashcard as JSON:
{{
  "front": "Question or prompt",
  "back": "Detailed answer with explanation",
  "tags": ["tag1", "tag2"],
  "difficulty": "{difficulty}"
}}

Return an array of {count} flashcards in JSON format.
''');

    _quizTemplate = PromptTemplate.fromTemplate('''
You are an expert quiz creator. Generate an adaptive quiz for:

Topic: {topic}
Concepts: {concepts}
Difficulty Level: {difficulty}
Number of Questions: {questionCount}

Create questions that:
1. Test understanding of key concepts
2. Include multiple choice, true/false, and short answer types
3. Provide clear explanations for correct answers
4. Are appropriate for the specified difficulty level
5. Build upon each other progressively

Format as JSON:
{{
  "title": "Quiz Title",
  "questions": [
    {{
      "question": "Question text",
      "type": "multiple_choice",
      "options": ["A", "B", "C", "D"],
      "correctAnswers": ["A"],
      "explanation": "Why this is correct",
      "concept": "Related concept",
      "points": 10
    }}
  ]
}}
''');

    _explanationTemplate = PromptTemplate.fromTemplate('''
You are an expert tutor. Explain the concept: {concept}

Context: {context}
Explanation Style: {style}

Provide an explanation that:
1. Is appropriate for the specified style
2. Uses clear, accessible language
3. Includes relevant examples
4. Connects to real-world applications
5. Builds understanding progressively

Style Guidelines:
- Simple: Use basic language, avoid jargon
- Detailed: Comprehensive explanation with technical details
- Analogy: Use real-world comparisons and metaphors
- Step-by-Step: Break down into clear sequential steps
- Visual: Describe visual representations and diagrams

Provide a clear, engaging explanation.
''');
  }

  /// Generates a learning plan using AI
  Future<LearningPlan> generateLearningPlan({
    required String subject,
    required String currentLevel,
    required List<String> learningGoals,
    required Map<String, dynamic> preferences,
  }) async {
    try {
      final prompt = _learningPlanTemplate.format({
        'subject': subject,
        'currentLevel': currentLevel,
        'learningGoals': learningGoals.join(', '),
        'preferences': preferences.toString(),
      });

      final response = await _chatModel.invoke(PromptValue.string(prompt));

      // Extract content from AI response
      final content = response.output.content;

      // TODO: Parse structured JSON from AI response for production use
      // For now, create a learning plan with AI-enhanced content
      return _createEnhancedLearningPlan(
        subject,
        currentLevel,
        learningGoals,
        content,
      );
    } catch (e) {
      // Log error and provide fallback
      print('Error generating learning plan: $e');
      // TODO: Implement proper error logging and user notification
      // Return a basic learning plan as fallback
      return _createMockLearningPlan(subject, currentLevel, learningGoals);
    }
  }

  /// Generates flashcards using AI
  Future<List<Flashcard>> generateFlashcards({
    required String topic,
    required int count,
    required DifficultyLevel difficulty,
    String? context,
  }) async {
    try {
      final prompt = _flashcardTemplate.format({
        'topic': topic,
        'count': count.toString(),
        'difficulty': difficulty.displayName.toLowerCase(),
        'context': context ?? 'General learning context',
      });

      final response = await _chatModel.invoke(PromptValue.string(prompt));

      // TODO: Parse structured JSON from AI response to create proper Flashcard objects
      // For now, return enhanced flashcards using AI response content
      final content = response.output.content;

      // Log the AI response for debugging
      print('AI Flashcard Response: $content');

      return _createEnhancedFlashcards(topic, count, difficulty, content);
    } catch (e) {
      // TODO: Implement proper error handling and logging
      print('Error generating flashcards: $e');
      // TODO: Return fallback mock flashcards if AI fails - using mock data
      return _createMockFlashcards(topic, count, difficulty);
    }
  }

  /// Generates a quiz using AI
  Future<Quiz> generateQuiz({
    required String topic,
    required List<String> concepts,
    required DifficultyLevel difficulty,
    int questionCount = 5,
  }) async {
    try {
      final prompt = _quizTemplate.format({
        'topic': topic,
        'concepts': concepts.join(', '),
        'difficulty': difficulty.displayName.toLowerCase(),
        'questionCount': questionCount.toString(),
      });

      final response = await _chatModel.invoke(PromptValue.string(prompt));

      // TODO: Parse structured JSON from AI response to create proper Quiz object
      // For now, return enhanced quiz using AI response content
      final content = response.output.content;

      // Log the AI response for debugging
      print('AI Quiz Response: $content');

      return _createEnhancedQuiz(topic, concepts, difficulty, content);
    } catch (e) {
      // TODO: Implement proper error handling and logging
      print('Error generating quiz: $e');
      // TODO: Return fallback mock quiz if AI fails - using mock data
      return _createMockQuiz(topic, concepts, difficulty);
    }
  }

  /// Explains a concept using AI
  Future<String> explainConcept({
    required String concept,
    required String context,
    required ExplanationStyle style,
  }) async {
    try {
      final prompt = _explanationTemplate.format({
        'concept': concept,
        'context': context,
        'style': style.displayName.toLowerCase(),
      });

      final response = await _chatModel.invoke(PromptValue.string(prompt));

      // TODO: Parse and format AI response for better presentation
      // Extract and return the explanation from AI response
      final content = response.output.content;

      // Log the AI response for debugging
      print('AI Concept Explanation Response: $content');

      return _createEnhancedExplanation(concept, style, content);
    } catch (e) {
      // TODO: Implement proper error handling and logging
      print('Error explaining concept: $e');
      // TODO: Return fallback mock explanation if AI fails - using mock data
      return _createMockExplanation(concept, style);
    }
  }

  /// Identifies knowledge gaps using AI analysis
  Future<List<String>> identifyKnowledgeGaps({
    required List<QuizResult> quizResults,
    required String subject,
  }) async {
    try {
      // TODO: Implement AI-powered pattern recognition for knowledge gap analysis
      // Knowledge gap analysis - currently using basic incorrect answer tracking
      final incorrectConcepts = <String>[];

      for (final result in quizResults) {
        for (final answer in result.answers) {
          if (!answer.isCorrect) {
            incorrectConcepts.add(answer.concept);
          }
        }
      }

      // TODO: Enhance with AI pattern recognition to suggest specific learning areas
      // Basic gap analysis for now
      return incorrectConcepts.toSet().toList();
    } catch (e) {
      // TODO: Implement proper error logging and user notification
      print('Error in knowledge gap analysis: $e');
      throw Exception('Failed to identify knowledge gaps: $e');
    }
  }

  // Enhanced methods that incorporate AI responses

  /// Creates an enhanced learning plan using AI content
  LearningPlan _createEnhancedLearningPlan(
    String subject,
    String currentLevel,
    List<String> learningGoals,
    String aiContent,
  ) {
    final now = DateTime.now();

    // Extract key information from AI content (simplified approach)
    // In production, you would parse structured JSON from the AI response
    final description = aiContent.isNotEmpty
        ? aiContent.length > 500
              ? '${aiContent.substring(0, 500)}...'
              : aiContent
        : 'A comprehensive learning plan created by AI based on your goals and current level.';

    return LearningPlan(
      id: 'plan_${now.millisecondsSinceEpoch}',
      userId: FirebaseAuth.instance.currentUser?.uid ?? 'anonymous',
      subject: subject,
      title: 'AI-Generated $subject Learning Plan',
      description: description,
      learningGoals: learningGoals,
      difficulty: _parseDifficultyFromLevel(currentLevel),
      startDate: now,
      targetEndDate: now.add(const Duration(days: 30)), // Default 30 days
      milestones: _generateMilestones(subject, learningGoals),
      preferences: {
        'aiGenerated': true,
        'currentLevel': currentLevel,
        'generatedAt': now.toIso8601String(),
      },
      createdAt: now,
      lastUpdated: now,
    );
  }

  /// Helper method to parse difficulty level from string
  DifficultyLevel _parseDifficultyFromLevel(String currentLevel) {
    switch (currentLevel.toLowerCase()) {
      case 'beginner':
      case 'easy':
        return DifficultyLevel.easy;
      case 'intermediate':
      case 'medium':
        return DifficultyLevel.medium;
      case 'advanced':
      case 'hard':
        return DifficultyLevel.hard;
      case 'expert':
        return DifficultyLevel.expert;
      default:
        return DifficultyLevel.medium; // Default to medium
    }
  }

  /// Helper method to generate milestones from learning goals
  List<LearningMilestone> _generateMilestones(
    String subject,
    List<String> learningGoals,
  ) {
    final now = DateTime.now();
    final milestones = <LearningMilestone>[];

    for (int i = 0; i < learningGoals.length; i++) {
      final goal = learningGoals[i];
      final targetDate = now.add(
        Duration(days: (i + 1) * 7),
      ); // Weekly milestones

      milestones.add(
        LearningMilestone(
          id: 'milestone_${now.millisecondsSinceEpoch}_$i',
          title: 'Master: $goal',
          description:
              'Complete understanding and practical application of $goal concepts',
          concepts: [
            goal,
          ], // Simplified - in reality would break down into sub-concepts
          targetDate: targetDate,
          isCompleted: false,
          resources: [
            'Study materials for $goal',
            'Practice exercises',
            'Assessment quiz',
          ],
          metadata: {
            'subject': subject,
            'goalIndex': i,
            'estimatedHours': 10 + (i * 5), // Increasing complexity
          },
        ),
      );
    }

    return milestones;
  }

  // Mock methods for development - TODO: Replace with actual AI response parsing

  /// Creates a mock learning plan
  /// TODO: Replace with AI parsing of structured JSON response
  LearningPlan _createMockLearningPlan(
    String subject,
    String currentLevel,
    List<String> learningGoals,
  ) {
    // TODO: Mock learning plan generation - will be enhanced with AI response parsing
    final now = DateTime.now();

    return LearningPlan(
      id: 'plan_${now.millisecondsSinceEpoch}',
      userId: FirebaseAuth.instance.currentUser?.uid ?? 'anonymous',
      subject: subject,
      title: 'AI-Generated $subject Learning Plan',
      description:
          'A comprehensive learning plan created by AI based on your goals and current level.',
      milestones: learningGoals.asMap().entries.map((entry) {
        final index = entry.key;
        final goal = entry.value;

        return LearningMilestone(
          id: 'milestone_${goal.hashCode}',
          title: 'Master $goal',
          description:
              'Complete understanding and practical application of $goal concepts.',
          concepts: [goal],
          targetDate: now.add(Duration(days: (index + 1) * 14)),
          isCompleted: false,
          resources: [
            'AI-Curated Study Materials for $goal',
            'Interactive Exercises: $goal',
            'Practice Problems: $goal',
          ],
          metadata: {
            'aiGenerated': true,
            'difficulty': currentLevel.toLowerCase(),
            'estimatedHours': 10 + (index * 5),
          },
        );
      }).toList(),
      startDate: now,
      targetEndDate: now.add(Duration(days: learningGoals.length * 14)),
      difficulty: _parseDifficultyLevel(currentLevel),
      learningGoals: learningGoals,
      preferences: {},
      createdAt: now,
      lastUpdated: now,
    );
  }

  /// Creates mock flashcards (TODO: Replace with AI parsing)
  List<Flashcard> _createMockFlashcards(
    String topic,
    int count,
    DifficultyLevel difficulty,
  ) {
    // Mock flashcard generation - will be enhanced with AI response parsing
    final now = DateTime.now();

    return List.generate(count, (index) {
      return Flashcard(
        id: 'ai_flashcard_${now.millisecondsSinceEpoch}_$index',
        front: 'AI-Generated Question ${index + 1} about $topic',
        back:
            'AI-Generated comprehensive answer explaining the concept with examples and practical applications.',
        subject:
            'AI-Generated Subject', // Will be extracted from AI response when parsing is implemented
        topic: topic,
        tags: [topic.toLowerCase(), 'ai-generated'],
        difficulty: difficulty,
        createdAt: now,
        lastReviewed: now,
        nextReview: now.add(const Duration(days: 1)),
        reviewCount: 0,
        easeFactor: 2.5,
        interval: 1,
      );
    });
  }

  /// Creates a mock quiz (TODO: Replace with AI parsing)
  Quiz _createMockQuiz(
    String topic,
    List<String> concepts,
    DifficultyLevel difficulty,
  ) {
    // Mock quiz generation - will be enhanced with AI response parsing
    final now = DateTime.now();

    final questions = concepts.take(5).map((concept) {
      return QuizQuestion(
        id: 'ai_question_${concept.hashCode}',
        question:
            'AI-Generated question about $concept in the context of $topic?',
        type: QuestionType.multipleChoice,
        options: [
          'AI-Generated Option A for $concept',
          'AI-Generated Option B for $concept',
          'AI-Generated Option C for $concept',
          'AI-Generated Option D for $concept',
        ],
        correctAnswers: ['AI-Generated Option A for $concept'],
        explanation:
            'AI-Generated explanation of why this answer is correct, with detailed reasoning.',
        concept: concept,
        difficulty: difficulty,
        points: 10,
      );
    }).toList();

    return Quiz(
      id: 'ai_quiz_${now.millisecondsSinceEpoch}',
      title: 'AI-Generated Adaptive Quiz: $topic',
      subject:
          'AI-Generated Subject', // Will be extracted from AI response when parsing is implemented
      topic: topic,
      questions: questions,
      difficulty: difficulty,
      createdAt: now,
      timeLimit: 30,
      isAdaptive: true,
      metadata: {
        'aiGenerated': true,
        'concepts': concepts,
        'generatedAt': now.toIso8601String(),
      },
    );
  }

  /// Creates enhanced flashcards using AI response content
  List<Flashcard> _createEnhancedFlashcards(
    String topic,
    int count,
    DifficultyLevel difficulty,
    String aiContent,
  ) {
    final now = DateTime.now();

    return List.generate(count, (index) {
      return Flashcard(
        id: 'ai_flashcard_${now.millisecondsSinceEpoch}_$index',
        front: 'AI-Enhanced Question ${index + 1} about $topic',
        back:
            'AI-Enhanced answer based on: ${aiContent.length > 100 ? "${aiContent.substring(0, 100)}..." : aiContent}',
        subject: _extractSubjectFromContent(aiContent, topic),
        topic: topic,
        tags: [topic.toLowerCase(), 'ai-enhanced'],
        difficulty: difficulty,
        createdAt: now,
        lastReviewed: now,
        nextReview: now.add(const Duration(days: 1)),
        reviewCount: 0,
        easeFactor: 2.5,
        interval: 1,
      );
    });
  }

  /// Creates enhanced quiz using AI response content
  Quiz _createEnhancedQuiz(
    String topic,
    List<String> concepts,
    DifficultyLevel difficulty,
    String aiContent,
  ) {
    final now = DateTime.now();

    final questions = concepts.take(5).map((concept) {
      return QuizQuestion(
        id: 'ai_question_${concept.hashCode}',
        question:
            'AI-Enhanced question about $concept in the context of $topic?',
        type: QuestionType.multipleChoice,
        options: [
          'AI-Enhanced Option A for $concept',
          'AI-Enhanced Option B for $concept',
          'AI-Enhanced Option C for $concept',
          'AI-Enhanced Option D for $concept',
        ],
        correctAnswers: ['AI-Enhanced Option A for $concept'],
        explanation:
            'AI-Enhanced explanation based on: ${aiContent.length > 50 ? "${aiContent.substring(0, 50)}..." : aiContent}',
        concept: concept,
        difficulty: difficulty,
        points: 10,
      );
    }).toList();

    return Quiz(
      id: 'ai_quiz_${now.millisecondsSinceEpoch}',
      title: 'AI-Enhanced Adaptive Quiz: $topic',
      subject: _extractSubjectFromContent(aiContent, topic),
      topic: topic,
      questions: questions,
      difficulty: difficulty,
      createdAt: now,
      timeLimit: 30,
      isAdaptive: true,
      metadata: {
        'aiGenerated': true,
        'concepts': concepts,
        'generatedAt': now.toIso8601String(),
      },
    );
  }

  /// Extracts subject from AI content or defaults to topic-based subject
  String _extractSubjectFromContent(String content, String topic) {
    // Simple subject extraction - can be enhanced with better parsing
    final lowerContent = content.toLowerCase();
    if (lowerContent.contains('math') ||
        lowerContent.contains('algebra') ||
        lowerContent.contains('calculus')) {
      return 'Mathematics';
    } else if (lowerContent.contains('science') ||
        lowerContent.contains('physics') ||
        lowerContent.contains('chemistry')) {
      return 'Science';
    } else if (lowerContent.contains('history') ||
        lowerContent.contains('historical')) {
      return 'History';
    } else if (lowerContent.contains('language') ||
        lowerContent.contains('literature')) {
      return 'Language Arts';
    }
    return 'General'; // Default fallback
  }

  /// Creates enhanced explanation using AI response content
  String _createEnhancedExplanation(
    String concept,
    ExplanationStyle style,
    String aiContent,
  ) {
    // Use AI content if available, otherwise fallback to mock
    if (aiContent.isNotEmpty && aiContent.length > 10) {
      return 'AI-Enhanced ${style.displayName.toLowerCase()} explanation of $concept:\n\n$aiContent';
    }
    return _createMockExplanation(concept, style);
  }

  /// Creates a mock explanation (Enhanced with AI content awareness)
  String _createMockExplanation(String concept, ExplanationStyle style) {
    return 'AI-Generated ${style.displayName.toLowerCase()} explanation of $concept. '
        'This would contain a comprehensive explanation tailored to the ${style.displayName.toLowerCase()} style, '
        'with appropriate examples, analogies, and level of detail.';
  }

  /// Helper method to parse difficulty level from string
  DifficultyLevel _parseDifficultyLevel(String level) {
    switch (level.toLowerCase()) {
      case 'easy':
      case 'beginner':
        return DifficultyLevel.easy;
      case 'medium':
      case 'intermediate':
        return DifficultyLevel.medium;
      case 'hard':
      case 'advanced':
        return DifficultyLevel.hard;
      case 'expert':
        return DifficultyLevel.expert;
      default:
        return DifficultyLevel.medium;
    }
  }
}
