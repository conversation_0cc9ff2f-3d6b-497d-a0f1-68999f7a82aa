import 'package:equatable/equatable.dart';
import 'flashcard.dart'; // For DifficultyLevel

/// Represents the learning progress for a user
class LearningProgress extends Equatable {
  final String id;
  final String userId;
  final String subject;
  final String topic;
  final double overallProgress; // 0.0 to 1.0
  final Map<String, double> conceptProgress; // concept -> progress (0.0 to 1.0)
  final List<String> masteredConcepts;
  final List<String> strugglingConcepts;
  final DateTime lastUpdated;
  final LearningStats stats;
  final Map<String, dynamic> metadata;

  const LearningProgress({
    required this.id,
    required this.userId,
    required this.subject,
    required this.topic,
    required this.overallProgress,
    required this.conceptProgress,
    required this.masteredConcepts,
    required this.strugglingConcepts,
    required this.lastUpdated,
    required this.stats,
    required this.metadata,
  });

  /// Creates a copy of this learning progress with the given fields replaced
  LearningProgress copyWith({
    String? id,
    String? userId,
    String? subject,
    String? topic,
    double? overallProgress,
    Map<String, double>? conceptProgress,
    List<String>? masteredConcepts,
    List<String>? strugglingConcepts,
    DateTime? lastUpdated,
    LearningStats? stats,
    Map<String, dynamic>? metadata,
  }) {
    return LearningProgress(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      subject: subject ?? this.subject,
      topic: topic ?? this.topic,
      overallProgress: overallProgress ?? this.overallProgress,
      conceptProgress: conceptProgress ?? this.conceptProgress,
      masteredConcepts: masteredConcepts ?? this.masteredConcepts,
      strugglingConcepts: strugglingConcepts ?? this.strugglingConcepts,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      stats: stats ?? this.stats,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Gets the overall progress as a percentage
  double get progressPercentage => overallProgress * 100;

  /// Gets the number of concepts being tracked
  int get totalConcepts => conceptProgress.length;

  /// Gets the number of mastered concepts
  int get masteredCount => masteredConcepts.length;

  /// Gets the number of struggling concepts
  int get strugglingCount => strugglingConcepts.length;

  /// Checks if the user has mastered a specific concept
  bool hasMasteredConcept(String concept) {
    return masteredConcepts.contains(concept);
  }

  /// Checks if the user is struggling with a specific concept
  bool isStrugglingWithConcept(String concept) {
    return strugglingConcepts.contains(concept);
  }

  /// Gets the progress for a specific concept
  double getConceptProgress(String concept) {
    return conceptProgress[concept] ?? 0.0;
  }

  @override
  List<Object> get props => [
        id,
        userId,
        subject,
        topic,
        overallProgress,
        conceptProgress,
        masteredConcepts,
        strugglingConcepts,
        lastUpdated,
        stats,
        metadata,
      ];

  @override
  String toString() {
    return 'LearningProgress(subject: $subject, topic: $topic, progress: ${progressPercentage.toStringAsFixed(1)}%)';
  }
}

/// Represents learning statistics for a user
class LearningStats extends Equatable {
  final int totalStudyTime; // in minutes
  final int sessionsCompleted;
  final int flashcardsReviewed;
  final int quizzesCompleted;
  final double averageQuizScore;
  final int streakDays;
  final DateTime lastStudyDate;
  final Map<String, int> weeklyActivity; // day -> minutes studied

  const LearningStats({
    required this.totalStudyTime,
    required this.sessionsCompleted,
    required this.flashcardsReviewed,
    required this.quizzesCompleted,
    required this.averageQuizScore,
    required this.streakDays,
    required this.lastStudyDate,
    required this.weeklyActivity,
  });

  /// Creates a copy of this learning stats with the given fields replaced
  LearningStats copyWith({
    int? totalStudyTime,
    int? sessionsCompleted,
    int? flashcardsReviewed,
    int? quizzesCompleted,
    double? averageQuizScore,
    int? streakDays,
    DateTime? lastStudyDate,
    Map<String, int>? weeklyActivity,
  }) {
    return LearningStats(
      totalStudyTime: totalStudyTime ?? this.totalStudyTime,
      sessionsCompleted: sessionsCompleted ?? this.sessionsCompleted,
      flashcardsReviewed: flashcardsReviewed ?? this.flashcardsReviewed,
      quizzesCompleted: quizzesCompleted ?? this.quizzesCompleted,
      averageQuizScore: averageQuizScore ?? this.averageQuizScore,
      streakDays: streakDays ?? this.streakDays,
      lastStudyDate: lastStudyDate ?? this.lastStudyDate,
      weeklyActivity: weeklyActivity ?? this.weeklyActivity,
    );
  }

  /// Gets the total study time in hours
  double get totalStudyHours => totalStudyTime / 60.0;

  /// Gets the average session duration in minutes
  double get averageSessionDuration {
    if (sessionsCompleted == 0) return 0.0;
    return totalStudyTime / sessionsCompleted;
  }

  /// Checks if the user studied today
  bool get studiedToday {
    final today = DateTime.now();
    return lastStudyDate.year == today.year &&
        lastStudyDate.month == today.month &&
        lastStudyDate.day == today.day;
  }

  /// Gets the total activity for the current week
  int get weeklyTotal => weeklyActivity.values.fold(0, (sum, minutes) => sum + minutes);

  @override
  List<Object> get props => [
        totalStudyTime,
        sessionsCompleted,
        flashcardsReviewed,
        quizzesCompleted,
        averageQuizScore,
        streakDays,
        lastStudyDate,
        weeklyActivity,
      ];

  @override
  String toString() {
    return 'LearningStats(totalTime: ${totalStudyHours.toStringAsFixed(1)}h, sessions: $sessionsCompleted, streak: $streakDays days)';
  }
}

/// Represents a learning plan for a user
class LearningPlan extends Equatable {
  final String id;
  final String userId;
  final String subject;
  final String title;
  final String description;
  final List<LearningMilestone> milestones;
  final DateTime startDate;
  final DateTime targetEndDate;
  final DifficultyLevel difficulty;
  final List<String> learningGoals;
  final Map<String, dynamic> preferences;
  final DateTime createdAt;
  final DateTime lastUpdated;

  const LearningPlan({
    required this.id,
    required this.userId,
    required this.subject,
    required this.title,
    required this.description,
    required this.milestones,
    required this.startDate,
    required this.targetEndDate,
    required this.difficulty,
    required this.learningGoals,
    required this.preferences,
    required this.createdAt,
    required this.lastUpdated,
  });

  /// Creates a copy of this learning plan with the given fields replaced
  LearningPlan copyWith({
    String? id,
    String? userId,
    String? subject,
    String? title,
    String? description,
    List<LearningMilestone>? milestones,
    DateTime? startDate,
    DateTime? targetEndDate,
    DifficultyLevel? difficulty,
    List<String>? learningGoals,
    Map<String, dynamic>? preferences,
    DateTime? createdAt,
    DateTime? lastUpdated,
  }) {
    return LearningPlan(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      subject: subject ?? this.subject,
      title: title ?? this.title,
      description: description ?? this.description,
      milestones: milestones ?? this.milestones,
      startDate: startDate ?? this.startDate,
      targetEndDate: targetEndDate ?? this.targetEndDate,
      difficulty: difficulty ?? this.difficulty,
      learningGoals: learningGoals ?? this.learningGoals,
      preferences: preferences ?? this.preferences,
      createdAt: createdAt ?? this.createdAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// Gets the total duration of the learning plan
  Duration get totalDuration => targetEndDate.difference(startDate);

  /// Gets the number of completed milestones
  int get completedMilestones => milestones.where((m) => m.isCompleted).length;

  /// Gets the overall progress of the learning plan
  double get progress {
    if (milestones.isEmpty) return 0.0;
    return completedMilestones / milestones.length;
  }

  /// Gets the progress as a percentage
  double get progressPercentage => progress * 100;

  @override
  List<Object> get props => [
        id,
        userId,
        subject,
        title,
        description,
        milestones,
        startDate,
        targetEndDate,
        difficulty,
        learningGoals,
        preferences,
        createdAt,
        lastUpdated,
      ];

  @override
  String toString() {
    return 'LearningPlan(title: $title, subject: $subject, progress: ${progressPercentage.toStringAsFixed(1)}%)';
  }
}

/// Represents a milestone in a learning plan
class LearningMilestone extends Equatable {
  final String id;
  final String title;
  final String description;
  final List<String> concepts;
  final DateTime targetDate;
  final bool isCompleted;
  final DateTime? completedAt;
  final List<String> resources;
  final Map<String, dynamic> metadata;

  const LearningMilestone({
    required this.id,
    required this.title,
    required this.description,
    required this.concepts,
    required this.targetDate,
    required this.isCompleted,
    this.completedAt,
    required this.resources,
    required this.metadata,
  });

  /// Creates a copy of this milestone with the given fields replaced
  LearningMilestone copyWith({
    String? id,
    String? title,
    String? description,
    List<String>? concepts,
    DateTime? targetDate,
    bool? isCompleted,
    DateTime? completedAt,
    List<String>? resources,
    Map<String, dynamic>? metadata,
  }) {
    return LearningMilestone(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      concepts: concepts ?? this.concepts,
      targetDate: targetDate ?? this.targetDate,
      isCompleted: isCompleted ?? this.isCompleted,
      completedAt: completedAt ?? this.completedAt,
      resources: resources ?? this.resources,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Checks if the milestone is overdue
  bool get isOverdue => !isCompleted && DateTime.now().isAfter(targetDate);

  /// Gets the time remaining until the target date
  Duration get timeRemaining {
    final now = DateTime.now();
    if (now.isAfter(targetDate)) return Duration.zero;
    return targetDate.difference(now);
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        concepts,
        targetDate,
        isCompleted,
        completedAt,
        resources,
        metadata,
      ];

  @override
  String toString() {
    return 'LearningMilestone(title: $title, completed: $isCompleted)';
  }
}
