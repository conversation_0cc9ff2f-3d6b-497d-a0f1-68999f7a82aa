import 'package:equatable/equatable.dart';

/// Represents a flashcard in the spaced repetition system
class Flashcard extends Equatable {
  final String id;
  final String front;
  final String back;
  final String subject;
  final String topic;
  final List<String> tags;
  final DifficultyLevel difficulty;
  final DateTime createdAt;
  final DateTime lastReviewed;
  final DateTime nextReview;
  final int reviewCount;
  final double easeFactor;
  final int interval;

  const Flashcard({
    required this.id,
    required this.front,
    required this.back,
    required this.subject,
    required this.topic,
    required this.tags,
    required this.difficulty,
    required this.createdAt,
    required this.lastReviewed,
    required this.nextReview,
    required this.reviewCount,
    required this.easeFactor,
    required this.interval,
  });

  /// Creates a copy of this flashcard with the given fields replaced
  Flashcard copyWith({
    String? id,
    String? front,
    String? back,
    String? subject,
    String? topic,
    List<String>? tags,
    DifficultyLevel? difficulty,
    DateTime? createdAt,
    DateTime? lastReviewed,
    DateTime? nextReview,
    int? reviewCount,
    double? easeFactor,
    int? interval,
  }) {
    return Flashcard(
      id: id ?? this.id,
      front: front ?? this.front,
      back: back ?? this.back,
      subject: subject ?? this.subject,
      topic: topic ?? this.topic,
      tags: tags ?? this.tags,
      difficulty: difficulty ?? this.difficulty,
      createdAt: createdAt ?? this.createdAt,
      lastReviewed: lastReviewed ?? this.lastReviewed,
      nextReview: nextReview ?? this.nextReview,
      reviewCount: reviewCount ?? this.reviewCount,
      easeFactor: easeFactor ?? this.easeFactor,
      interval: interval ?? this.interval,
    );
  }

  /// Checks if the flashcard is due for review
  bool get isDue => DateTime.now().isAfter(nextReview);

  /// Checks if this is a new flashcard (never reviewed)
  bool get isNew => reviewCount == 0;

  /// Gets the time until next review
  Duration get timeUntilReview {
    final now = DateTime.now();
    if (now.isAfter(nextReview)) {
      return Duration.zero;
    }
    return nextReview.difference(now);
  }

  @override
  List<Object> get props => [
        id,
        front,
        back,
        subject,
        topic,
        tags,
        difficulty,
        createdAt,
        lastReviewed,
        nextReview,
        reviewCount,
        easeFactor,
        interval,
      ];

  @override
  String toString() {
    return 'Flashcard(id: $id, subject: $subject, topic: $topic, difficulty: $difficulty)';
  }
}

/// Enum representing the difficulty level of a flashcard
enum DifficultyLevel {
  easy,
  medium,
  hard,
  expert,
}

/// Extension to provide human-readable names and colors for difficulty levels
extension DifficultyLevelExtension on DifficultyLevel {
  String get displayName {
    switch (this) {
      case DifficultyLevel.easy:
        return 'Easy';
      case DifficultyLevel.medium:
        return 'Medium';
      case DifficultyLevel.hard:
        return 'Hard';
      case DifficultyLevel.expert:
        return 'Expert';
    }
  }

  /// Returns a color associated with the difficulty level
  int get colorValue {
    switch (this) {
      case DifficultyLevel.easy:
        return 0xFF4CAF50; // Green
      case DifficultyLevel.medium:
        return 0xFFFF9800; // Orange
      case DifficultyLevel.hard:
        return 0xFFF44336; // Red
      case DifficultyLevel.expert:
        return 0xFF9C27B0; // Purple
    }
  }

  /// Returns the numerical value for sorting
  int get value {
    switch (this) {
      case DifficultyLevel.easy:
        return 1;
      case DifficultyLevel.medium:
        return 2;
      case DifficultyLevel.hard:
        return 3;
      case DifficultyLevel.expert:
        return 4;
    }
  }
}

/// Enum representing user response to a flashcard during review
enum FlashcardResponse {
  hard,
  good,
  easy,
}

/// Extension for flashcard response
extension FlashcardResponseExtension on FlashcardResponse {
  String get displayName {
    switch (this) {
      case FlashcardResponse.hard:
        return 'Hard';
      case FlashcardResponse.good:
        return 'Good';
      case FlashcardResponse.easy:
        return 'Easy';
    }
  }

  /// Returns the quality score used in spaced repetition algorithm
  int get qualityScore {
    switch (this) {
      case FlashcardResponse.hard:
        return 1;
      case FlashcardResponse.good:
        return 3;
      case FlashcardResponse.easy:
        return 5;
    }
  }
}
