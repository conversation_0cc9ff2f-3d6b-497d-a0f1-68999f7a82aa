import 'package:equatable/equatable.dart';
import 'flashcard.dart'; // For DifficultyLevel

/// Represents a quiz in the AI tutor system
class Quiz extends Equatable {
  final String id;
  final String title;
  final String subject;
  final String topic;
  final List<QuizQuestion> questions;
  final DifficultyLevel difficulty;
  final DateTime createdAt;
  final int timeLimit; // in minutes
  final bool isAdaptive;
  final Map<String, dynamic> metadata;

  const Quiz({
    required this.id,
    required this.title,
    required this.subject,
    required this.topic,
    required this.questions,
    required this.difficulty,
    required this.createdAt,
    required this.timeLimit,
    required this.isAdaptive,
    required this.metadata,
  });

  /// Creates a copy of this quiz with the given fields replaced
  Quiz copyWith({
    String? id,
    String? title,
    String? subject,
    String? topic,
    List<QuizQuestion>? questions,
    DifficultyLevel? difficulty,
    DateTime? createdAt,
    int? timeLimit,
    bool? isAdaptive,
    Map<String, dynamic>? metadata,
  }) {
    return Quiz(
      id: id ?? this.id,
      title: title ?? this.title,
      subject: subject ?? this.subject,
      topic: topic ?? this.topic,
      questions: questions ?? this.questions,
      difficulty: difficulty ?? this.difficulty,
      createdAt: createdAt ?? this.createdAt,
      timeLimit: timeLimit ?? this.timeLimit,
      isAdaptive: isAdaptive ?? this.isAdaptive,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Gets the total number of questions in the quiz
  int get totalQuestions => questions.length;

  /// Gets the estimated completion time in minutes
  int get estimatedTime => (questions.length * 1.5).ceil(); // 1.5 minutes per question

  @override
  List<Object> get props => [
        id,
        title,
        subject,
        topic,
        questions,
        difficulty,
        createdAt,
        timeLimit,
        isAdaptive,
        metadata,
      ];

  @override
  String toString() {
    return 'Quiz(id: $id, title: $title, questions: ${questions.length})';
  }
}

/// Represents a single question in a quiz
class QuizQuestion extends Equatable {
  final String id;
  final String question;
  final QuestionType type;
  final List<String> options;
  final List<String> correctAnswers;
  final String explanation;
  final String concept;
  final DifficultyLevel difficulty;
  final int points;

  const QuizQuestion({
    required this.id,
    required this.question,
    required this.type,
    required this.options,
    required this.correctAnswers,
    required this.explanation,
    required this.concept,
    required this.difficulty,
    required this.points,
  });

  /// Creates a copy of this question with the given fields replaced
  QuizQuestion copyWith({
    String? id,
    String? question,
    QuestionType? type,
    List<String>? options,
    List<String>? correctAnswers,
    String? explanation,
    String? concept,
    DifficultyLevel? difficulty,
    int? points,
  }) {
    return QuizQuestion(
      id: id ?? this.id,
      question: question ?? this.question,
      type: type ?? this.type,
      options: options ?? this.options,
      correctAnswers: correctAnswers ?? this.correctAnswers,
      explanation: explanation ?? this.explanation,
      concept: concept ?? this.concept,
      difficulty: difficulty ?? this.difficulty,
      points: points ?? this.points,
    );
  }

  /// Checks if the given answers are correct
  bool isCorrect(List<String> userAnswers) {
    if (userAnswers.length != correctAnswers.length) return false;
    return userAnswers.every((answer) => correctAnswers.contains(answer));
  }

  /// Checks if this is a multiple choice question
  bool get isMultipleChoice => type == QuestionType.multipleChoice;

  /// Checks if this is a true/false question
  bool get isTrueFalse => type == QuestionType.trueFalse;

  /// Checks if this is a fill in the blank question
  bool get isFillInBlank => type == QuestionType.fillInBlank;

  @override
  List<Object> get props => [
        id,
        question,
        type,
        options,
        correctAnswers,
        explanation,
        concept,
        difficulty,
        points,
      ];

  @override
  String toString() {
    return 'QuizQuestion(id: $id, type: $type, concept: $concept)';
  }
}

/// Enum representing different types of quiz questions
enum QuestionType {
  multipleChoice,
  trueFalse,
  fillInBlank,
  shortAnswer,
  essay,
}

/// Extension for question type
extension QuestionTypeExtension on QuestionType {
  String get displayName {
    switch (this) {
      case QuestionType.multipleChoice:
        return 'Multiple Choice';
      case QuestionType.trueFalse:
        return 'True/False';
      case QuestionType.fillInBlank:
        return 'Fill in the Blank';
      case QuestionType.shortAnswer:
        return 'Short Answer';
      case QuestionType.essay:
        return 'Essay';
    }
  }

  /// Returns the icon name for the question type
  String get iconName {
    switch (this) {
      case QuestionType.multipleChoice:
        return 'radio_button_checked';
      case QuestionType.trueFalse:
        return 'check_box';
      case QuestionType.fillInBlank:
        return 'edit';
      case QuestionType.shortAnswer:
        return 'short_text';
      case QuestionType.essay:
        return 'article';
    }
  }
}

/// Represents the result of a quiz attempt
class QuizResult extends Equatable {
  final String id;
  final String quizId;
  final String userId;
  final List<QuizAnswer> answers;
  final double score;
  final int totalPoints;
  final DateTime startTime;
  final DateTime endTime;
  final Duration timeSpent;

  const QuizResult({
    required this.id,
    required this.quizId,
    required this.userId,
    required this.answers,
    required this.score,
    required this.totalPoints,
    required this.startTime,
    required this.endTime,
    required this.timeSpent,
  });

  /// Gets the percentage score
  double get percentage => (score / totalPoints) * 100;

  /// Gets the number of correct answers
  int get correctAnswers => answers.where((answer) => answer.isCorrect).length;

  /// Gets the total number of questions
  int get totalQuestions => answers.length;

  @override
  List<Object> get props => [
        id,
        quizId,
        userId,
        answers,
        score,
        totalPoints,
        startTime,
        endTime,
        timeSpent,
      ];
}

/// Represents a user's answer to a quiz question
class QuizAnswer extends Equatable {
  final String questionId;
  final List<String> userAnswers;
  final bool isCorrect;
  final int pointsEarned;
  final String concept;

  const QuizAnswer({
    required this.questionId,
    required this.userAnswers,
    required this.isCorrect,
    required this.pointsEarned,
    required this.concept,
  });

  @override
  List<Object> get props => [
        questionId,
        userAnswers,
        isCorrect,
        pointsEarned,
        concept,
      ];
}
