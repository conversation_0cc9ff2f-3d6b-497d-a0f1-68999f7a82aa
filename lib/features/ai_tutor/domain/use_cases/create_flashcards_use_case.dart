import 'package:dartz/dartz.dart';
import '../entities/flashcard.dart';
import '../repositories/ai_tutor_repository.dart';
import '../repositories/flashcard_repository.dart';
import '../../../../core/error/failures.dart';

/// Use case for creating flashcards
class CreateFlashcardsUseCase {
  final AITutorRepository _aiTutorRepository;
  final FlashcardRepository _flashcardRepository;

  CreateFlashcardsUseCase(
    this._aiTutorRepository,
    this._flashcardRepository,
  );

  /// Creates flashcards for a specific topic
  Future<Either<Failure, List<Flashcard>>> call(
    CreateFlashcardsParams params,
  ) async {
    try {
      // 1. Validate parameters
      final validationResult = FlashcardValidator.validateParams(params);
      if (validationResult.isLeft()) {
        return validationResult.fold(
          (failure) => Left(failure),
          (_) => const Right([]), // This won't be reached
        );
      }

      // 2. Generate flashcards using AI
      final flashcardsResult = await _aiTutorRepository.generateFlashcards(
        topic: params.topic,
        count: params.count,
        difficulty: params.difficulty,
        context: params.context,
      );

      return flashcardsResult.fold(
        (failure) => Left(failure),
        (flashcards) async {
          try {
            // 3. Save flashcards to repository
            final saveResult = await _flashcardRepository.saveFlashcards(flashcards);
            
            return saveResult.fold(
              (failure) => Left(failure),
              (_) => Right(flashcards),
            );
          } catch (e) {
            return Left(CacheFailure('Failed to save flashcards: ${e.toString()}'));
          }
        },
      );
    } catch (e) {
      return Left(ServerFailure('Failed to create flashcards: ${e.toString()}'));
    }
  }

  /// Creates flashcards from user-provided content
  Future<Either<Failure, List<Flashcard>>> createFromContent({
    required String content,
    required String subject,
    required String topic,
    required DifficultyLevel difficulty,
    int? maxCount,
  }) async {
    try {
      // Parse content and extract key concepts
      final concepts = _extractConcepts(content);
      
      if (concepts.isEmpty) {
        return const Left(ValidationFailure('No concepts found in the provided content'));
      }

      // Limit the number of flashcards if specified
      final count = maxCount != null ? concepts.length.clamp(1, maxCount) : concepts.length;
      
      // Generate flashcards based on extracted concepts
      final flashcards = concepts.take(count).map((concept) {
        return _createFlashcardFromConcept(
          concept: concept,
          subject: subject,
          topic: topic,
          difficulty: difficulty,
        );
      }).toList();

      // Save flashcards
      final saveResult = await _flashcardRepository.saveFlashcards(flashcards);
      
      return saveResult.fold(
        (failure) => Left(failure),
        (_) => Right(flashcards),
      );
    } catch (e) {
      return Left(DataFailure('Failed to create flashcards from content: ${e.toString()}'));
    }
  }

  /// Updates existing flashcards based on user feedback
  Future<Either<Failure, List<Flashcard>>> updateFlashcardsFromFeedback({
    required List<String> flashcardIds,
    required Map<String, String> feedback,
  }) async {
    try {
      final updatedFlashcards = <Flashcard>[];

      for (final flashcardId in flashcardIds) {
        final flashcardResult = await _flashcardRepository.getFlashcard(flashcardId);
        
        await flashcardResult.fold(
          (failure) async {
            // Log error but continue with other flashcards
          },
          (flashcard) async {
            if (flashcard != null && feedback.containsKey(flashcardId)) {
              final updatedFlashcard = _applyFeedbackToFlashcard(
                flashcard,
                feedback[flashcardId]!,
              );
              
              final saveResult = await _flashcardRepository.saveFlashcard(updatedFlashcard);
              saveResult.fold(
                (failure) {
                  // Log error but continue
                },
                (_) {
                  updatedFlashcards.add(updatedFlashcard);
                },
              );
            }
          },
        );
      }

      return Right(updatedFlashcards);
    } catch (e) {
      return Left(ServerFailure('Failed to update flashcards: ${e.toString()}'));
    }
  }

  /// Extracts key concepts from content
  List<String> _extractConcepts(String content) {
    // Simple concept extraction - can be enhanced with NLP
    final sentences = content.split(RegExp(r'[.!?]+'));
    final concepts = <String>[];

    for (final sentence in sentences) {
      final trimmed = sentence.trim();
      if (trimmed.isNotEmpty && trimmed.length > 10) {
        // Extract potential concepts (simplified)
        final words = trimmed.split(' ');
        if (words.length >= 3 && words.length <= 15) {
          concepts.add(trimmed);
        }
      }
    }

    return concepts.take(20).toList(); // Limit to 20 concepts
  }

  /// Creates a flashcard from a concept
  Flashcard _createFlashcardFromConcept({
    required String concept,
    required String subject,
    required String topic,
    required DifficultyLevel difficulty,
  }) {
    final now = DateTime.now();
    
    return Flashcard(
      id: 'flashcard_${now.millisecondsSinceEpoch}_${concept.hashCode}',
      front: 'What is $concept?',
      back: concept,
      subject: subject,
      topic: topic,
      tags: [subject.toLowerCase(), topic.toLowerCase()],
      difficulty: difficulty,
      createdAt: now,
      lastReviewed: now,
      nextReview: now.add(const Duration(days: 1)), // Initial interval
      reviewCount: 0,
      easeFactor: 2.5, // Default ease factor
      interval: 1, // Initial interval in days
    );
  }

  /// Applies user feedback to improve a flashcard
  Flashcard _applyFeedbackToFlashcard(Flashcard flashcard, String feedback) {
    // Simple feedback application - can be enhanced with AI
    String updatedBack = flashcard.back;
    
    if (feedback.toLowerCase().contains('too easy')) {
      // Increase difficulty or add more detail
      updatedBack = '${flashcard.back}\n\nAdditional context: [Enhanced based on feedback]';
    } else if (feedback.toLowerCase().contains('too hard')) {
      // Simplify or add explanation
      updatedBack = '${flashcard.back}\n\nSimplified: [Simplified based on feedback]';
    } else if (feedback.toLowerCase().contains('unclear')) {
      // Add clarification
      updatedBack = '${flashcard.back}\n\nClarification: [Clarified based on feedback]';
    }

    return flashcard.copyWith(back: updatedBack);
  }
}

/// Parameters for creating flashcards
class CreateFlashcardsParams {
  final String topic;
  final int count;
  final DifficultyLevel difficulty;
  final String? context;

  const CreateFlashcardsParams({
    required this.topic,
    required this.count,
    required this.difficulty,
    this.context,
  });

  @override
  String toString() {
    return 'CreateFlashcardsParams(topic: $topic, count: $count, difficulty: $difficulty)';
  }
}

/// Validation helper for flashcard parameters
class FlashcardValidator {
  /// Validates the parameters for creating flashcards
  static Either<Failure, void> validateParams(CreateFlashcardsParams params) {
    // Validate topic
    if (params.topic.trim().isEmpty) {
      return const Left(ValidationFailure('Topic cannot be empty'));
    }

    if (params.topic.length < 2) {
      return const Left(ValidationFailure('Topic must be at least 2 characters long'));
    }

    if (params.topic.length > 100) {
      return const Left(ValidationFailure('Topic must be less than 100 characters'));
    }

    // Validate count
    if (params.count <= 0) {
      return const Left(ValidationFailure('Count must be greater than 0'));
    }

    if (params.count > 50) {
      return const Left(ValidationFailure('Count cannot exceed 50 flashcards'));
    }

    // Validate context if provided
    if (params.context != null && params.context!.length > 1000) {
      return const Left(ValidationFailure('Context must be less than 1000 characters'));
    }

    return const Right(null);
  }

  /// Validates flashcard content
  static Either<Failure, void> validateFlashcardContent({
    required String front,
    required String back,
  }) {
    if (front.trim().isEmpty) {
      return const Left(ValidationFailure('Flashcard front cannot be empty'));
    }

    if (back.trim().isEmpty) {
      return const Left(ValidationFailure('Flashcard back cannot be empty'));
    }

    if (front.length > 500) {
      return const Left(ValidationFailure('Flashcard front must be less than 500 characters'));
    }

    if (back.length > 1000) {
      return const Left(ValidationFailure('Flashcard back must be less than 1000 characters'));
    }

    return const Right(null);
  }
}
