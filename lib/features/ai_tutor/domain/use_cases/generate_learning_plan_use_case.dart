import 'package:dartz/dartz.dart';
import '../entities/learning_progress.dart';
import '../repositories/ai_tutor_repository.dart';
import '../services/spaced_repetition_service.dart';
import '../services/feynman_technique_service.dart';
import '../../../../core/error/failures.dart';

/// Use case for generating personalized learning plans
class GenerateLearningPlanUseCase {
  final AITutorRepository _repository;
  final SpacedRepetitionService _spacedRepetitionService;
  final FeynmanTechniqueService _feynmanService;

  GenerateLearningPlanUseCase(
    this._repository,
    this._spacedRepetitionService,
    this._feynmanService,
  );

  /// Generates a personalized learning plan based on user preferences and goals
  Future<Either<Failure, LearningPlan>> call(
    GenerateLearningPlanParams params,
  ) async {
    try {
      // 1. Generate base learning plan using AI
      final basePlanResult = await _repository.generateLearningPlan(
        subject: params.subject,
        currentLevel: params.currentLevel,
        learningGoals: params.learningGoals,
        preferences: params.preferences,
      );

      return basePlanResult.fold(
        (failure) => Left(failure),
        (basePlan) async {
          try {
            // 2. Apply spaced repetition scheduling
            final scheduledPlan = await _spacedRepetitionService
                .applySpacedRepetition(basePlan);

            // 3. Integrate Feynman Technique milestones
            final enhancedPlan = await _feynmanService
                .addFeynmanMilestones(scheduledPlan);

            return Right(enhancedPlan);
          } catch (e) {
            return Left(AIFailure('Failed to enhance learning plan: ${e.toString()}'));
          }
        },
      );
    } catch (e) {
      return Left(ServerFailure('Failed to generate learning plan: ${e.toString()}'));
    }
  }
}

/// Parameters for generating a learning plan
class GenerateLearningPlanParams {
  final String subject;
  final String currentLevel;
  final List<String> learningGoals;
  final Map<String, dynamic> preferences;

  const GenerateLearningPlanParams({
    required this.subject,
    required this.currentLevel,
    required this.learningGoals,
    required this.preferences,
  });

  @override
  String toString() {
    return 'GenerateLearningPlanParams(subject: $subject, level: $currentLevel, goals: $learningGoals)';
  }
}

/// Validation helper for learning plan parameters
class LearningPlanValidator {
  /// Validates the parameters for generating a learning plan
  static Either<Failure, void> validateParams(GenerateLearningPlanParams params) {
    // Validate subject
    if (params.subject.trim().isEmpty) {
      return const Left(ValidationFailure('Subject cannot be empty'));
    }

    // Validate current level
    if (params.currentLevel.trim().isEmpty) {
      return const Left(ValidationFailure('Current level cannot be empty'));
    }

    // Validate learning goals
    if (params.learningGoals.isEmpty) {
      return const Left(ValidationFailure('At least one learning goal is required'));
    }

    // Check for empty goals
    if (params.learningGoals.any((goal) => goal.trim().isEmpty)) {
      return const Left(ValidationFailure('Learning goals cannot be empty'));
    }

    // Validate subject format
    if (!_isValidSubject(params.subject)) {
      return const Left(ValidationFailure('Invalid subject format'));
    }

    // Validate level format
    if (!_isValidLevel(params.currentLevel)) {
      return const Left(ValidationFailure('Invalid level format'));
    }

    return const Right(null);
  }

  static bool _isValidSubject(String subject) {
    // Basic validation - can be extended
    return subject.length >= 2 && subject.length <= 50;
  }

  static bool _isValidLevel(String level) {
    const validLevels = [
      'Beginner',
      'Elementary',
      'Intermediate',
      'Advanced',
      'Expert',
    ];
    return validLevels.contains(level);
  }
}

/// Helper class for learning plan generation
class LearningPlanHelper {
  /// Estimates the duration for a learning plan based on goals and level
  static Duration estimatePlanDuration({
    required String subject,
    required String currentLevel,
    required List<String> learningGoals,
  }) {
    // Base duration calculation
    int baseDays = 30; // Default 30 days

    // Adjust based on level
    switch (currentLevel.toLowerCase()) {
      case 'beginner':
        baseDays = 60;
        break;
      case 'elementary':
        baseDays = 45;
        break;
      case 'intermediate':
        baseDays = 30;
        break;
      case 'advanced':
        baseDays = 21;
        break;
      case 'expert':
        baseDays = 14;
        break;
    }

    // Adjust based on number of goals
    final goalMultiplier = (learningGoals.length * 0.5).clamp(0.5, 2.0);
    baseDays = (baseDays * goalMultiplier).round();

    return Duration(days: baseDays);
  }

  /// Suggests study time per day based on goals and available time
  static Duration suggestDailyStudyTime({
    required List<String> learningGoals,
    required Duration totalDuration,
    Duration? availableTimePerDay,
  }) {
    // Default to 30 minutes per day
    int minutesPerDay = 30;

    // Adjust based on number of goals
    minutesPerDay = (learningGoals.length * 15).clamp(15, 120);

    // Consider available time if provided
    if (availableTimePerDay != null) {
      minutesPerDay = minutesPerDay.clamp(15, availableTimePerDay.inMinutes);
    }

    return Duration(minutes: minutesPerDay);
  }

  /// Generates milestone titles based on learning goals
  static List<String> generateMilestoneTitles(List<String> learningGoals) {
    return learningGoals.map((goal) => 'Master $goal').toList();
  }

  /// Calculates difficulty progression for milestones
  static List<String> calculateDifficultyProgression(String currentLevel) {
    const levelProgression = {
      'Beginner': ['Beginner', 'Elementary', 'Intermediate'],
      'Elementary': ['Elementary', 'Intermediate', 'Advanced'],
      'Intermediate': ['Intermediate', 'Advanced', 'Expert'],
      'Advanced': ['Advanced', 'Expert', 'Expert'],
      'Expert': ['Expert', 'Expert', 'Expert'],
    };

    return levelProgression[currentLevel] ?? ['Intermediate', 'Advanced', 'Expert'];
  }
}
