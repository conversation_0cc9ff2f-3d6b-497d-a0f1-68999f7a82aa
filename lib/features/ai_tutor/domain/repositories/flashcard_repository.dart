import 'package:dartz/dartz.dart';
import '../entities/flashcard.dart';
import '../../../../core/error/failures.dart';

/// Repository interface for flashcard operations
abstract class FlashcardRepository {
  /// Saves a flashcard to the repository
  Future<Either<Failure, void>> saveFlashcard(Flashcard flashcard);

  /// Saves multiple flashcards to the repository
  Future<Either<Failure, void>> saveFlashcards(List<Flashcard> flashcards);

  /// Retrieves a flashcard by its ID
  Future<Either<Failure, Flashcard?>> getFlashcard(String id);

  /// Retrieves flashcards for a specific topic
  Future<Either<Failure, List<Flashcard>>> getFlashcardsByTopic({
    required String topic,
    String? subject,
    DifficultyLevel? difficulty,
  });

  /// Retrieves flashcards for a specific subject
  Future<Either<Failure, List<Flashcard>>> getFlashcardsBySubject(String subject);

  /// Retrieves flashcards that are due for review
  Future<Either<Failure, List<Flashcard>>> getDueFlashcards({
    String? userId,
    String? subject,
    String? topic,
  });

  /// Updates a flashcard after review (spaced repetition algorithm)
  Future<Either<Failure, Flashcard>> updateFlashcardAfterReview({
    required String flashcardId,
    required FlashcardResponse response,
  });

  /// Deletes a flashcard
  Future<Either<Failure, void>> deleteFlashcard(String id);

  /// Deletes multiple flashcards
  Future<Either<Failure, void>> deleteFlashcards(List<String> ids);

  /// Searches flashcards by content
  Future<Either<Failure, List<Flashcard>>> searchFlashcards({
    required String query,
    String? subject,
    String? topic,
    DifficultyLevel? difficulty,
  });

  /// Gets flashcard statistics for a user
  Future<Either<Failure, FlashcardStats>> getFlashcardStats({
    String? userId,
    String? subject,
    String? topic,
  });

  /// Exports flashcards to a specific format
  Future<Either<Failure, String>> exportFlashcards({
    required List<String> flashcardIds,
    required ExportFormat format,
  });

  /// Imports flashcards from a specific format
  Future<Either<Failure, List<Flashcard>>> importFlashcards({
    required String data,
    required ExportFormat format,
  });

  /// Gets the next flashcard for review based on spaced repetition
  Future<Either<Failure, Flashcard?>> getNextReviewFlashcard({
    String? userId,
    String? subject,
    String? topic,
  });

  /// Marks a flashcard as favorite
  Future<Either<Failure, void>> toggleFlashcardFavorite(String flashcardId);

  /// Gets favorite flashcards
  Future<Either<Failure, List<Flashcard>>> getFavoriteFlashcards({
    String? userId,
    String? subject,
  });

  /// Gets flashcard review history
  Future<Either<Failure, List<FlashcardReview>>> getFlashcardReviewHistory({
    required String flashcardId,
    int? limit,
  });

  /// Resets flashcard progress (starts spaced repetition from beginning)
  Future<Either<Failure, void>> resetFlashcardProgress(String flashcardId);
}

/// Represents flashcard statistics
class FlashcardStats {
  final int totalFlashcards;
  final int dueFlashcards;
  final int newFlashcards;
  final int reviewedToday;
  final int masteredFlashcards;
  final double averageEaseFactor;
  final Map<DifficultyLevel, int> difficultyDistribution;
  final Map<String, int> subjectDistribution;

  const FlashcardStats({
    required this.totalFlashcards,
    required this.dueFlashcards,
    required this.newFlashcards,
    required this.reviewedToday,
    required this.masteredFlashcards,
    required this.averageEaseFactor,
    required this.difficultyDistribution,
    required this.subjectDistribution,
  });

  /// Gets the percentage of mastered flashcards
  double get masteryPercentage {
    if (totalFlashcards == 0) return 0.0;
    return (masteredFlashcards / totalFlashcards) * 100;
  }

  /// Gets the percentage of due flashcards
  double get duePercentage {
    if (totalFlashcards == 0) return 0.0;
    return (dueFlashcards / totalFlashcards) * 100;
  }

  @override
  String toString() {
    return 'FlashcardStats(total: $totalFlashcards, due: $dueFlashcards, mastered: $masteredFlashcards)';
  }
}

/// Represents a flashcard review record
class FlashcardReview {
  final String id;
  final String flashcardId;
  final DateTime reviewDate;
  final FlashcardResponse response;
  final int intervalBefore;
  final int intervalAfter;
  final double easeFactorBefore;
  final double easeFactorAfter;
  final Duration reviewTime;

  const FlashcardReview({
    required this.id,
    required this.flashcardId,
    required this.reviewDate,
    required this.response,
    required this.intervalBefore,
    required this.intervalAfter,
    required this.easeFactorBefore,
    required this.easeFactorAfter,
    required this.reviewTime,
  });

  @override
  String toString() {
    return 'FlashcardReview(id: $id, response: $response, date: $reviewDate)';
  }
}

/// Enum representing different export/import formats
enum ExportFormat {
  json,
  csv,
  anki,
  quizlet,
}

/// Extension for export format
extension ExportFormatExtension on ExportFormat {
  String get displayName {
    switch (this) {
      case ExportFormat.json:
        return 'JSON';
      case ExportFormat.csv:
        return 'CSV';
      case ExportFormat.anki:
        return 'Anki';
      case ExportFormat.quizlet:
        return 'Quizlet';
    }
  }

  String get fileExtension {
    switch (this) {
      case ExportFormat.json:
        return '.json';
      case ExportFormat.csv:
        return '.csv';
      case ExportFormat.anki:
        return '.apkg';
      case ExportFormat.quizlet:
        return '.txt';
    }
  }

  String get mimeType {
    switch (this) {
      case ExportFormat.json:
        return 'application/json';
      case ExportFormat.csv:
        return 'text/csv';
      case ExportFormat.anki:
        return 'application/zip';
      case ExportFormat.quizlet:
        return 'text/plain';
    }
  }
}
