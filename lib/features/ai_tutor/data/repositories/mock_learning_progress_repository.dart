import 'package:dartz/dartz.dart';
import '../../domain/entities/learning_progress.dart';
import '../../domain/entities/learning_session.dart';
import '../../domain/repositories/learning_progress_repository.dart';
import '../../../../core/error/failures.dart';

/// Mock implementation of LearningProgressRepository for development and testing
class MockLearningProgressRepository implements LearningProgressRepository {
  // In-memory storage for mock data
  final Map<String, LearningProgress> _progress = {};
  final Map<String, List<LearningSession>> _sessions = {};
  final Map<String, List<LearningGoal>> _goals = {};
  final Map<String, LearningStreak> _streaks = {};

  @override
  Future<Either<Failure, void>> saveLearningProgress(LearningProgress progress) async {
    await Future.delayed(const Duration(milliseconds: 100));
    final key = '${progress.userId}_${progress.subject}_${progress.topic}';
    _progress[key] = progress;
    return const Right(null);
  }

  @override
  Future<Either<Failure, LearningProgress?>> getLearningProgress({
    required String userId,
    required String subject,
    String? topic,
  }) async {
    await Future.delayed(const Duration(milliseconds: 100));
    final key = '${userId}_${subject}_${topic ?? ''}';
    return Right(_progress[key]);
  }

  @override
  Future<Either<Failure, List<LearningProgress>>> getUserLearningProgress(String userId) async {
    await Future.delayed(const Duration(milliseconds: 150));
    final userProgress = _progress.values
        .where((progress) => progress.userId == userId)
        .toList();
    return Right(userProgress);
  }

  @override
  Future<Either<Failure, void>> updateConceptProgress({
    required String userId,
    required String subject,
    required String concept,
    required double progress,
  }) async {
    await Future.delayed(const Duration(milliseconds: 50));
    
    final key = '${userId}_${subject}_';
    final existingProgress = _progress[key];
    
    if (existingProgress != null) {
      final updatedConceptProgress = Map<String, double>.from(existingProgress.conceptProgress);
      updatedConceptProgress[concept] = progress.clamp(0.0, 1.0);
      
      _progress[key] = existingProgress.copyWith(
        conceptProgress: updatedConceptProgress,
        lastUpdated: DateTime.now(),
      );
    }
    
    return const Right(null);
  }

  @override
  Future<Either<Failure, void>> markConceptAsMastered({
    required String userId,
    required String subject,
    required String concept,
  }) async {
    await Future.delayed(const Duration(milliseconds: 50));
    
    final key = '${userId}_${subject}_';
    final existingProgress = _progress[key];
    
    if (existingProgress != null) {
      final masteredConcepts = List<String>.from(existingProgress.masteredConcepts);
      final strugglingConcepts = List<String>.from(existingProgress.strugglingConcepts);
      
      if (!masteredConcepts.contains(concept)) {
        masteredConcepts.add(concept);
      }
      strugglingConcepts.remove(concept);
      
      _progress[key] = existingProgress.copyWith(
        masteredConcepts: masteredConcepts,
        strugglingConcepts: strugglingConcepts,
        lastUpdated: DateTime.now(),
      );
    }
    
    return const Right(null);
  }

  @override
  Future<Either<Failure, void>> markConceptAsStruggling({
    required String userId,
    required String subject,
    required String concept,
  }) async {
    await Future.delayed(const Duration(milliseconds: 50));
    
    final key = '${userId}_${subject}_';
    final existingProgress = _progress[key];
    
    if (existingProgress != null) {
      final strugglingConcepts = List<String>.from(existingProgress.strugglingConcepts);
      
      if (!strugglingConcepts.contains(concept)) {
        strugglingConcepts.add(concept);
      }
      
      _progress[key] = existingProgress.copyWith(
        strugglingConcepts: strugglingConcepts,
        lastUpdated: DateTime.now(),
      );
    }
    
    return const Right(null);
  }

  @override
  Future<Either<Failure, void>> removeConceptFromStruggling({
    required String userId,
    required String subject,
    required String concept,
  }) async {
    await Future.delayed(const Duration(milliseconds: 50));
    
    final key = '${userId}_${subject}_';
    final existingProgress = _progress[key];
    
    if (existingProgress != null) {
      final strugglingConcepts = List<String>.from(existingProgress.strugglingConcepts);
      strugglingConcepts.remove(concept);
      
      _progress[key] = existingProgress.copyWith(
        strugglingConcepts: strugglingConcepts,
        lastUpdated: DateTime.now(),
      );
    }
    
    return const Right(null);
  }

  @override
  Future<Either<Failure, void>> updateLearningStats({
    required String userId,
    required String subject,
    required LearningStats stats,
  }) async {
    await Future.delayed(const Duration(milliseconds: 50));
    
    final key = '${userId}_${subject}_';
    final existingProgress = _progress[key];
    
    if (existingProgress != null) {
      _progress[key] = existingProgress.copyWith(
        stats: stats,
        lastUpdated: DateTime.now(),
      );
    }
    
    return const Right(null);
  }

  @override
  Future<Either<Failure, void>> recordLearningSession(LearningSession session) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    _sessions.putIfAbsent(session.userId, () => []);
    _sessions[session.userId]!.add(session);
    
    return const Right(null);
  }

  @override
  Future<Either<Failure, List<LearningSession>>> getLearningSessionsForUser({
    required String userId,
    String? subject,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    var sessions = _sessions[userId] ?? [];
    
    if (subject != null) {
      sessions = sessions.where((session) => session.subject == subject).toList();
    }
    
    if (startDate != null) {
      sessions = sessions.where((session) => session.startTime.isAfter(startDate)).toList();
    }
    
    if (endDate != null) {
      sessions = sessions.where((session) => session.startTime.isBefore(endDate)).toList();
    }
    
    return Right(sessions);
  }

  @override
  Future<Either<Failure, LearningAnalytics>> getLearningAnalytics({
    required String userId,
    String? subject,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    await Future.delayed(const Duration(milliseconds: 200));
    
    final sessionsResult = await getLearningSessionsForUser(
      userId: userId,
      subject: subject,
      startDate: startDate,
      endDate: endDate,
    );
    
    return sessionsResult.fold(
      (failure) => Left(failure),
      (sessions) {
        final analytics = _calculateAnalytics(userId, sessions, startDate, endDate);
        return Right(analytics);
      },
    );
  }

  @override
  Future<Either<Failure, LearningStreak>> getLearningStreak(String userId) async {
    await Future.delayed(const Duration(milliseconds: 50));
    
    final streak = _streaks[userId] ?? LearningStreak(
      userId: userId,
      currentStreak: 0,
      longestStreak: 0,
      lastStudyDate: DateTime.now().subtract(const Duration(days: 1)),
      streakStartDate: DateTime.now(),
      studyDates: [],
    );
    
    return Right(streak);
  }

  @override
  Future<Either<Failure, void>> updateDailyActivity({
    required String userId,
    required DateTime date,
    required int minutesStudied,
  }) async {
    await Future.delayed(const Duration(milliseconds: 50));
    
    // Update streak
    final currentStreak = _streaks[userId];
    if (currentStreak != null) {
      final updatedDates = List<DateTime>.from(currentStreak.studyDates);
      final dateOnly = DateTime(date.year, date.month, date.day);
      
      if (!updatedDates.any((d) => 
          d.year == dateOnly.year && 
          d.month == dateOnly.month && 
          d.day == dateOnly.day)) {
        updatedDates.add(dateOnly);
      }
      
      _streaks[userId] = currentStreak.copyWith(
        lastStudyDate: date,
        studyDates: updatedDates,
      );
    }
    
    return const Right(null);
  }

  @override
  Future<Either<Failure, Map<String, int>>> getWeeklyActivity({
    required String userId,
    required DateTime weekStart,
  }) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    // Mock weekly activity data
    final activity = <String, int>{};
    for (int i = 0; i < 7; i++) {
      final date = weekStart.add(Duration(days: i));
      final key = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      activity[key] = (i * 15) % 60; // Mock minutes studied
    }
    
    return Right(activity);
  }

  @override
  Future<Either<Failure, List<LearningGoal>>> getLearningGoals(String userId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return Right(_goals[userId] ?? []);
  }

  @override
  Future<Either<Failure, void>> saveLearningGoal(LearningGoal goal) async {
    await Future.delayed(const Duration(milliseconds: 50));
    
    _goals.putIfAbsent(goal.userId, () => []);
    final existingIndex = _goals[goal.userId]!.indexWhere((g) => g.id == goal.id);
    
    if (existingIndex >= 0) {
      _goals[goal.userId]![existingIndex] = goal;
    } else {
      _goals[goal.userId]!.add(goal);
    }
    
    return const Right(null);
  }

  @override
  Future<Either<Failure, void>> updateLearningGoalProgress({
    required String goalId,
    required double progress,
  }) async {
    await Future.delayed(const Duration(milliseconds: 50));
    
    for (final userGoals in _goals.values) {
      final goalIndex = userGoals.indexWhere((goal) => goal.id == goalId);
      if (goalIndex >= 0) {
        final goal = userGoals[goalIndex];
        userGoals[goalIndex] = goal.copyWith(
          currentProgress: progress.clamp(0.0, 1.0),
          isCompleted: progress >= goal.targetProgress,
          completedAt: progress >= goal.targetProgress ? DateTime.now() : null,
        );
        break;
      }
    }
    
    return const Right(null);
  }

  @override
  Future<Either<Failure, void>> deleteLearningGoal(String goalId) async {
    await Future.delayed(const Duration(milliseconds: 50));
    
    for (final userGoals in _goals.values) {
      userGoals.removeWhere((goal) => goal.id == goalId);
    }
    
    return const Right(null);
  }

  @override
  Future<Either<Failure, List<LearningRecommendation>>> getLearningRecommendations({
    required String userId,
    String? subject,
  }) async {
    await Future.delayed(const Duration(milliseconds: 150));
    
    // Mock recommendations based on user progress
    final recommendations = [
      LearningRecommendation(
        id: 'rec_1',
        title: 'Review Weak Concepts',
        description: 'Focus on concepts where you scored below 70%',
        type: RecommendationType.reviewWeakConcepts,
        priority: 5,
        subject: subject ?? 'General',
        estimatedTime: const Duration(minutes: 30),
        metadata: {},
      ),
      LearningRecommendation(
        id: 'rec_2',
        title: 'Practice Flashcards',
        description: 'Review due flashcards to maintain memory',
        type: RecommendationType.practiceFlashcards,
        priority: 4,
        subject: subject ?? 'General',
        estimatedTime: const Duration(minutes: 15),
        metadata: {},
      ),
      LearningRecommendation(
        id: 'rec_3',
        title: 'Take Quiz',
        description: 'Test your knowledge with adaptive questions',
        type: RecommendationType.takeQuiz,
        priority: 3,
        subject: subject ?? 'General',
        estimatedTime: const Duration(minutes: 20),
        metadata: {},
      ),
    ];
    
    return Right(recommendations);
  }

  /// Helper method to calculate learning analytics
  LearningAnalytics _calculateAnalytics(
    String userId,
    List<LearningSession> sessions,
    DateTime? startDate,
    DateTime? endDate,
  ) {
    final now = DateTime.now();
    final start = startDate ?? now.subtract(const Duration(days: 30));
    final end = endDate ?? now;
    
    final totalStudyTime = sessions
        .where((session) => session.duration != null)
        .map((session) => session.duration!.inMinutes)
        .fold(0, (sum, minutes) => sum + minutes);
    
    final averageSessionDuration = sessions.isEmpty 
        ? 0.0 
        : totalStudyTime / sessions.length;
    
    final subjectTimeDistribution = <String, int>{};
    for (final session in sessions) {
      final minutes = session.duration?.inMinutes ?? 0;
      subjectTimeDistribution[session.subject] = 
          (subjectTimeDistribution[session.subject] ?? 0) + minutes;
    }
    
    final conceptMasteryRates = <String, double>{};
    for (final session in sessions) {
      for (final concept in session.conceptsCovered) {
        conceptMasteryRates[concept] = 
            (conceptMasteryRates[concept] ?? 0.0) + (session.comprehensionScore / 100);
      }
    }
    
    // Normalize mastery rates
    conceptMasteryRates.updateAll((key, value) => 
        (value / sessions.where((s) => s.conceptsCovered.contains(key)).length).clamp(0.0, 1.0));
    
    final dailyActivities = _generateDailyActivities(start, end, sessions);
    
    return LearningAnalytics(
      userId: userId,
      startDate: start,
      endDate: end,
      totalStudyTime: totalStudyTime,
      totalSessions: sessions.length,
      averageSessionDuration: averageSessionDuration,
      subjectTimeDistribution: subjectTimeDistribution,
      conceptMasteryRates: conceptMasteryRates,
      dailyActivities: dailyActivities,
      overallProgress: conceptMasteryRates.values.isEmpty 
          ? 0.0 
          : conceptMasteryRates.values.reduce((a, b) => a + b) / conceptMasteryRates.length,
      streakDays: _streaks[userId]?.currentStreak ?? 0,
    );
  }

  /// Helper method to generate daily activities
  List<DailyActivity> _generateDailyActivities(
    DateTime start,
    DateTime end,
    List<LearningSession> sessions,
  ) {
    final activities = <DailyActivity>[];
    final current = DateTime(start.year, start.month, start.day);
    final endDate = DateTime(end.year, end.month, end.day);
    
    while (!current.isAfter(endDate)) {
      final dayStart = current;
      final dayEnd = current.add(const Duration(days: 1));
      
      final daySessions = sessions.where((session) =>
          session.startTime.isAfter(dayStart) && session.startTime.isBefore(dayEnd)).toList();
      
      final minutesStudied = daySessions
          .map((session) => session.duration?.inMinutes ?? 0)
          .fold(0, (sum, minutes) => sum + minutes);
      
      final subjectsStudied = daySessions.map((session) => session.subject).toSet().toList();
      
      final averageScore = daySessions.isEmpty 
          ? 0.0 
          : daySessions.map((session) => session.comprehensionScore)
              .reduce((a, b) => a + b) / daySessions.length;
      
      activities.add(DailyActivity(
        date: current,
        minutesStudied: minutesStudied,
        sessionsCompleted: daySessions.length,
        subjectsStudied: subjectsStudied,
        averageScore: averageScore,
      ));
      
      current.add(const Duration(days: 1));
    }
    
    return activities;
  }
}
