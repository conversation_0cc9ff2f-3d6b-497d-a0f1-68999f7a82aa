import 'package:dio/dio.dart';
import 'package:diogeneschatbot/features/offline_image/presentation/pages/index/controller.dart';
import 'package:diogeneschatbot/services/tts_service.dart';
import 'package:get_it/get_it.dart';
import 'package:diogeneschatbot/services/auth_service.dart';
import 'package:flutter/material.dart';
import 'package:diogeneschatbot/features/offline_image/domain/controller/model_service.dart';
import 'package:diogeneschatbot/features/offline_image/domain/controller/model_stroage_service.dart';
import 'package:diogeneschatbot/features/offline_image/presentation/pages/index/downloader.dart';
import 'package:diogeneschatbot/features/offline_image/presentation/pages/index/generator.dart';
import 'package:diogeneschatbot/features/offline_image/presentation/pages/index/loader.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:diogeneschatbot/models/post.dart'; // Import PostRepository

// AI Tutor imports
import 'package:diogeneschatbot/features/ai_tutor/presentation/bloc/ai_tutor_bloc.dart';
import 'package:diogeneschatbot/features/ai_tutor/domain/use_cases/generate_learning_plan_use_case.dart';
import 'package:diogeneschatbot/features/ai_tutor/domain/use_cases/create_flashcards_use_case.dart';
import 'package:diogeneschatbot/features/ai_tutor/domain/use_cases/track_progress_use_case.dart';
import 'package:diogeneschatbot/features/ai_tutor/domain/use_cases/generate_quiz_use_case.dart';
import 'package:diogeneschatbot/features/ai_tutor/domain/use_cases/explain_concept_use_case.dart';
import 'package:diogeneschatbot/features/ai_tutor/domain/use_cases/identify_knowledge_gaps_use_case.dart';
import 'package:diogeneschatbot/features/ai_tutor/domain/use_cases/load_study_recommendations_use_case.dart';
import 'package:diogeneschatbot/features/ai_tutor/domain/services/spaced_repetition_service.dart';
import 'package:diogeneschatbot/features/ai_tutor/domain/services/feynman_technique_service.dart';
import 'package:diogeneschatbot/features/ai_tutor/domain/services/ai_content_service.dart';
import 'package:diogeneschatbot/features/ai_tutor/domain/repositories/ai_tutor_repository.dart';
import 'package:diogeneschatbot/features/ai_tutor/domain/repositories/flashcard_repository.dart';
import 'package:diogeneschatbot/features/ai_tutor/domain/repositories/learning_progress_repository.dart';
import 'package:diogeneschatbot/features/ai_tutor/data/repositories/mock_ai_tutor_repository.dart';
import 'package:diogeneschatbot/features/ai_tutor/data/repositories/mock_flashcard_repository.dart';
import 'package:diogeneschatbot/features/ai_tutor/data/repositories/mock_learning_progress_repository.dart';

final getIt = GetIt.instance;

Future<void> setupGetIt() async {
  // Register your services here
  getIt.registerSingleton<AuthService>(AuthService());
  getIt.registerLazySingleton<PostRepository>(
    () => PostRepository(),
  ); // Register PostRepository
  // Register ValueNotifier for locale
  // Load locale from SharedPreferences
  SharedPreferences prefs = await SharedPreferences.getInstance();
  String? savedLocale = prefs.getString('locale');
  Locale initialLocale = savedLocale != null
      ? Locale(savedLocale)
      : Locale('en');

  // Register ValueNotifier for locale
  getIt.registerSingleton<ValueNotifier<Locale>>(ValueNotifier(initialLocale));

  getIt.registerSingleton<TTSService>(TTSService());

  getIt.registerSingleton<Dio>(Dio());
  getIt.registerSingleton<ModelService>(ModelService());
  getIt.registerSingleton<ModelStroageService>(ModelStroageService());
  getIt.registerSingleton<IndexPageModelDownloader>(IndexPageModelDownloader());
  getIt.registerSingleton<IndexPageModelLoader>(IndexPageModelLoader());
  getIt.registerSingleton<IndexPageGenerator>(IndexPageGenerator());
  getIt.registerSingleton<IndexPageController>(IndexPageController());

  // Setup AI Tutor dependencies
  setupAITutorDependencies();
}

/// Sets up AI Tutor feature dependencies
void setupAITutorDependencies() {
  // Repositories - Mock implementations for now
  // TODO: Replace with actual implementations that use real databases and APIs
  // TODO: Implement Firebase/Firestore repositories for production use
  getIt.registerLazySingleton<AITutorRepository>(
    () => MockAITutorRepository(aiContentService: getIt<AIContentService>()),
  );
  getIt.registerLazySingleton<FlashcardRepository>(
    () =>
        MockFlashcardRepository(), // TODO: Replace with actual FlashcardRepository
  );
  getIt.registerLazySingleton<LearningProgressRepository>(
    () =>
        MockLearningProgressRepository(), // TODO: Replace with actual LearningProgressRepository
  );

  // Services
  getIt.registerLazySingleton(() => SpacedRepetitionService());
  getIt.registerLazySingleton(() => FeynmanTechniqueService());
  // AI content service configured with API key from dotenv
  getIt.registerLazySingleton(() => AIContentService());

  // Use cases
  getIt.registerLazySingleton(
    () => GenerateLearningPlanUseCase(
      getIt<AITutorRepository>(),
      getIt<SpacedRepetitionService>(),
      getIt<FeynmanTechniqueService>(),
    ),
  );

  getIt.registerLazySingleton(
    () => CreateFlashcardsUseCase(
      getIt<AITutorRepository>(),
      getIt<FlashcardRepository>(),
    ),
  );

  getIt.registerLazySingleton(
    () => TrackProgressUseCase(getIt<LearningProgressRepository>()),
  );

  getIt.registerLazySingleton(
    () => GenerateQuizUseCase(getIt<AITutorRepository>()),
  );

  getIt.registerLazySingleton(
    () => ExplainConceptUseCase(getIt<AITutorRepository>()),
  );

  getIt.registerLazySingleton(
    () => IdentifyKnowledgeGapsUseCase(getIt<AITutorRepository>()),
  );

  getIt.registerLazySingleton(
    () => LoadStudyRecommendationsUseCase(getIt<AITutorRepository>()),
  );

  // BLoCs
  getIt.registerFactory(
    () => AITutorBloc(
      generateLearningPlan: getIt<GenerateLearningPlanUseCase>(),
      createFlashcards: getIt<CreateFlashcardsUseCase>(),
      trackProgress: getIt<TrackProgressUseCase>(),
      generateQuiz: getIt<GenerateQuizUseCase>(),
      explainConcept: getIt<ExplainConceptUseCase>(),
      identifyKnowledgeGaps: getIt<IdentifyKnowledgeGapsUseCase>(),
      loadStudyRecommendations: getIt<LoadStudyRecommendationsUseCase>(),
    ),
  );
}
